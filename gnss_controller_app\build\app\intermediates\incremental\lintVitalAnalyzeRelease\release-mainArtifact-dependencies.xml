<dependencies>
  <compile
      roots="__local_aars__:D:\UM980 APP\gnss_controller_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar,D:\UM980 APP\gnss_controller_app\android@@:flutter_blue_plus_android::release,D:\UM980 APP\gnss_controller_app\android@@:flutter_bluetooth_serial::release,D:\UM980 APP\gnss_controller_app\android@@:path_provider_android::release,D:\UM980 APP\gnss_controller_app\android@@:permission_handler_android::release,D:\UM980 APP\gnss_controller_app\android@@:shared_preferences_android::release,D:\UM980 APP\gnss_controller_app\android@@:webview_flutter_android::release,io.flutter:flutter_embedding_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar,androidx.window:window-java:1.0.0-beta04@aar,androidx.window:window:1.0.0-beta04@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.10@jar,io.flutter:armeabi_v7a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar,io.flutter:arm64_v8a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar,io.flutter:x86_64_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar,androidx.lifecycle:lifecycle-common-java8:2.2.0@jar,androidx.fragment:fragment:1.3.4@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity:1.2.3@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.6.0@aar,androidx.core:core:1.6.0@aar,androidx.lifecycle:lifecycle-runtime:2.3.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar,androidx.lifecycle:lifecycle-common:2.3.1@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar,androidx.savedstate:savedstate:1.1.0@aar,androidx.annotation:annotation-jvm:1.8.2@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar,org.jetbrains:annotations:13.0@jar,androidx.annotation:annotation-experimental:1.1.0@aar,androidx.tracing:tracing:1.0.0@aar">
    <dependency
        name="__local_aars__:D:\UM980 APP\gnss_controller_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:D:\UM980 APP\gnss_controller_app\build\app\intermediates\flutter\release\libs.jar"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:flutter_blue_plus_android::release"
        simpleName="com.lib.flutter_blue_plus:flutter_blue_plus_android"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:flutter_bluetooth_serial::release"
        simpleName="io.github.edufolly.flutterbluetoothserial:flutter_bluetooth_serial"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:path_provider_android::release"
        simpleName="io.flutter.plugins.pathprovider:path_provider_android"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:permission_handler_android::release"
        simpleName="com.baseflow.permissionhandler:permission_handler_android"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:shared_preferences_android::release"
        simpleName="io.flutter.plugins.sharedpreferences:shared_preferences_android"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:webview_flutter_android::release"
        simpleName="io.flutter.plugins.webviewflutter:webview_flutter_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.window:window-java:1.0.0-beta04@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.window:window:1.0.0-beta04@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.2.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.fragment:fragment:1.3.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.2.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.6.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.savedstate:savedstate:1.1.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.2@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.1.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
  </compile>
  <package
      roots="__local_aars__:D:\UM980 APP\gnss_controller_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar,D:\UM980 APP\gnss_controller_app\android@@:flutter_blue_plus_android::release,D:\UM980 APP\gnss_controller_app\android@@:flutter_bluetooth_serial::release,D:\UM980 APP\gnss_controller_app\android@@:path_provider_android::release,D:\UM980 APP\gnss_controller_app\android@@:permission_handler_android::release,D:\UM980 APP\gnss_controller_app\android@@:shared_preferences_android::release,D:\UM980 APP\gnss_controller_app\android@@:webview_flutter_android::release,androidx.datastore:datastore-preferences:1.0.0@aar,androidx.datastore:datastore:1.0.0@aar,io.flutter:flutter_embedding_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar,androidx.window:window-java:1.0.0-beta04@aar,androidx.datastore:datastore-preferences-core:1.0.0@jar,androidx.datastore:datastore-core:1.0.0@jar,androidx.window:window:1.0.0-beta04@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.10@jar,io.flutter:armeabi_v7a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar,io.flutter:arm64_v8a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar,io.flutter:x86_64_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar,androidx.appcompat:appcompat:1.3.0@aar,androidx.webkit:webkit:1.12.0@aar,androidx.lifecycle:lifecycle-common-java8:2.2.0@jar,androidx.fragment:fragment:1.3.4@aar,androidx.activity:activity:1.2.3@aar,androidx.tracing:tracing:1.0.0@aar,androidx.appcompat:appcompat-resources:1.3.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.6.0@aar,androidx.core:core:1.6.0@aar,androidx.lifecycle:lifecycle-runtime:2.3.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar,androidx.savedstate:savedstate:1.1.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar,androidx.lifecycle:lifecycle-common:2.3.1@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar,androidx.annotation:annotation-jvm:1.8.2@jar,org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar,org.jetbrains:annotations:13.0@jar,androidx.annotation:annotation-experimental:1.1.0@aar">
    <dependency
        name="__local_aars__:D:\UM980 APP\gnss_controller_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:D:\UM980 APP\gnss_controller_app\build\app\intermediates\flutter\release\libs.jar"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:flutter_blue_plus_android::release"
        simpleName="com.lib.flutter_blue_plus:flutter_blue_plus_android"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:flutter_bluetooth_serial::release"
        simpleName="io.github.edufolly.flutterbluetoothserial:flutter_bluetooth_serial"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:path_provider_android::release"
        simpleName="io.flutter.plugins.pathprovider:path_provider_android"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:permission_handler_android::release"
        simpleName="com.baseflow.permissionhandler:permission_handler_android"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:shared_preferences_android::release"
        simpleName="io.flutter.plugins.sharedpreferences:shared_preferences_android"/>
    <dependency
        name="D:\UM980 APP\gnss_controller_app\android@@:webview_flutter_android::release"
        simpleName="io.flutter.plugins.webviewflutter:webview_flutter_android"/>
    <dependency
        name="androidx.datastore:datastore-preferences:1.0.0@aar"
        simpleName="androidx.datastore:datastore-preferences"/>
    <dependency
        name="androidx.datastore:datastore:1.0.0@aar"
        simpleName="androidx.datastore:datastore"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.window:window-java:1.0.0-beta04@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core:1.0.0@jar"
        simpleName="androidx.datastore:datastore-preferences-core"/>
    <dependency
        name="androidx.datastore:datastore-core:1.0.0@jar"
        simpleName="androidx.datastore:datastore-core"/>
    <dependency
        name="androidx.window:window:1.0.0-beta04@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="androidx.appcompat:appcompat:1.3.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.webkit:webkit:1.12.0@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.2.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.fragment:fragment:1.3.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.2.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.3.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.6.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.savedstate:savedstate:1.1.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.2@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.1.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
  </package>
</dependencies>
