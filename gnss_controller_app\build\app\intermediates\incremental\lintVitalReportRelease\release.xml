<variant
    name="release"
    package="com.example.gnss_controller_app"
    minSdkVersion="21"
    targetSdkVersion="34"
    shrinking="true"
    mergedManifest="D:\UM980 APP\gnss_controller_app\build\app\intermediates\merged_manifest\release\AndroidManifest.xml"
    manifestMergeReport="D:\UM980 APP\gnss_controller_app\build\app\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\UM980 APP\gnss_controller_app\build\app\intermediates\default_proguard_files\global\proguard-android.txt-7.3.0;C:\development\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="D:\UM980 APP\gnss_controller_app\build\app\intermediates\lint_vital_partial_results\release\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifest="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin"
        resDirectories="src\main\res"
        assetsDirectories="src\main\assets"/>
    <sourceProvider
        manifest="src\release\AndroidManifest.xml"
        javaDirectories="src\release\java;src\release\kotlin"
        resDirectories="src\release\res"
        assetsDirectories="src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <mainArtifact
      classOutputs="D:\UM980 APP\gnss_controller_app\build\app\intermediates\javac\release\classes;D:\UM980 APP\gnss_controller_app\build\app\tmp\kotlin-classes\release;D:\UM980 APP\gnss_controller_app\build\app\kotlinToolingMetadata;D:\UM980 APP\gnss_controller_app\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      applicationId="com.example.gnss_controller_app"
      generatedSourceFolders="D:\UM980 APP\gnss_controller_app\build\app\generated\ap_generated_sources\release\out;D:\UM980 APP\gnss_controller_app\build\app\generated\aidl_source_output_dir\release\out;D:\UM980 APP\gnss_controller_app\build\app\generated\source\buildConfig\release;D:\UM980 APP\gnss_controller_app\build\app\generated\renderscript_source_output_dir\release\out"
      generatedResourceFolders="D:\UM980 APP\gnss_controller_app\build\app\generated\res\rs\release;D:\UM980 APP\gnss_controller_app\build\app\generated\res\resValues\release">
  </mainArtifact>
</variant>
