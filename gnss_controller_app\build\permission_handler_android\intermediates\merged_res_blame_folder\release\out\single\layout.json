[{"merged": "com.baseflow.permissionhandler.permission_handler_android-merged_res-10:/layout/notification_template_part_chronometer.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-core-1.6.0-0:/layout/notification_template_part_chronometer.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-merged_res-10:/layout/notification_action_tombstone.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-core-1.6.0-0:/layout/notification_action_tombstone.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-merged_res-10:/layout/notification_template_part_time.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-core-1.6.0-0:/layout/notification_template_part_time.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-merged_res-10:/layout/notification_template_icon_group.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-core-1.6.0-0:/layout/notification_template_icon_group.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-merged_res-10:/layout/custom_dialog.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-core-1.6.0-0:/layout/custom_dialog.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-merged_res-10:/layout/notification_action.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-core-1.6.0-0:/layout/notification_action.xml"}]