android.content.res.Resources#getIdentifier present: true
Web content present: true
Referenced Strings:
cancel
android.settings.MANAGE_APP_ALL_FILES...
ADVERTISING_TIMEOUT
callerContext
DISMISS
valueCase_
app_flutter
kRepeat
java.lang.Integer
PICTURES
OnConnectionStateChanged
setLayoutDirection
android.speech.extra.RESULTS_PENDINGI...
preferences_pb
WEB_MESSAGE_LISTENER
GeneratedPluginsRegister
java.lang.CharSequence
devices
androidx.window.layout.WindowInfoTrac...
PermissionHandler.AppSettingsManager
click
keyframe
0
POST_WEB_MESSAGE
ACTION_PRESS_AND_HOLD
left
kotlinx.coroutines.DefaultExecutor
ERROR_GATT_WRITE_REQUEST_BUSY
registerWith
SystemSoundType.alert
removeItemAt
produceFile
REDUCE_MOTION
$splitPairFilters
kotlinx.coroutines.flow.SubscribedFlo...
createBond
sidecarAdapter
flutter_bluetooth_serial/methods
io.flutter.plugins.sharedpreferences....
SystemUiMode.immersiveSticky
flutter/platform_views
android_uses_fine_location
addFontWeightStyle
TextCapitalization.words
connect
_
android.bluetooth.device.extra.RSSI
dev.flutter.pigeon.webview_flutter_an...
rssi
b
address
SUCCESS
user_query
ACTION_CLEAR_ACCESSIBILITY_FOCUS
destroy_engine_with_activity
truncated
CONNECTION_REJECTED_NO_SUITABLE_CHANNEL
effectiveDirectAddress
kotlin.String
getFactory
sidecarDeviceState
INSTANT_PASSED
UNSPECIFIED
r
component
SUSPEND
java.lang.Module
TypefaceCompatApi26Impl
android_legacy
splitPairFilters
x
SystemUiMode.edgeToEdge
androidx.core.view.inputmethod.InputC...
propertyXName
displayFeature.rect
mimeType
dev.flutter.pigeon.webview_flutter_an...
isTagEnabled
getLoadedPackageInfo
startIndex
emailAddress
with_service_data
dev.flutter.pigeon.shared_preferences...
componentName
ACTION_SCROLL_FORWARD
OPEN_MULTIPLE
LMP_OR_LL_RESPONSE_TIMEOUT
kotlinx.coroutines.default.parallelism
extensionCallback
preferencesProto.preferencesMap
gps
list
LONG_PRESS
AnnotateVersionCheck
HAS_CHECKED_STATE
UNSET_PRIMARY_NAV
GATT_ENCRYPTED_NO_MITM
CONNECTION_ACCEPT_TIMEOUT_EXCEEDED
FlutterEngine
flutter/keyevent
dev.flutter.pigeon.webview_flutter_an...
success
AppLifecycleState.
GATT_INSUFFICIENT_RESOURCES
SystemChrome.setSystemUIOverlayStyle
androidx.view.accessibility.Accessibi...
readCharacteristic
repeatMode
COMPLETING_WAITING_CHILDREN
enable_state_restoration
_invoked
locale
SDK_INT
KeyEmbedderResponder
android.permission.WRITE_CONTACTS
requestRelaunchActivity
_delayed
android.os.Build
FIXED32_LIST_PACKED
android.permission.RECEIVE_SMS
MOVE_CURSOR_BACKWARD_BY_CHARACTER
kotlin.collections.List
EmbeddingBackend
MOVE_CURSOR_FORWARD_BY_CHARACTER
org.chromium.support_lib_glue.Support...
args
LIMIT_REACHED
resizeUpLeft
GATT_MORE
SearchView
service
Preferences.kt
TextInputType.emailAddress
bluetoothUnavailable
readDataOrHandleCorruption
ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE
android.view.View$AttachInfo
FlutterActivity
java.lang.Float
Dispatchers.IO
focus
android.settings.REQUEST_SCHEDULE_EXA...
EmptyQueue
dev.flutter.pigeon.webview_flutter_an...
embeddingExtension
WEB_MESSAGE_PORT_CLOSE
PAUSED
SCOPES_ROUTE
android.os.Build$VERSION
executor
TooltipCompatHandler
continuous_divisor
write
onStop
withResponse
dev.flutter.pigeon.shared_preferences...
LOCKED
byte
getDeferredComponentInstallState
handlePairingRequest
splitRule
dev.flutter.pigeon.webview_flutter_an...
resizeUp
creditCardNumber
DiscouragedApi
onPostResume
doAfterTextChanged
SAFE_BROWSING_HIT
bluetooth_address
wait
birthDateYear
FlutterActivityAndFragmentDelegate
onReadRemoteRssi:
%s.%sParcelizer
CHARACTERS
TypefaceCompatApi24Impl
top
io.flutter.embedding.android.EnableVu...
ACTION_PAGE_UP
it.parameterTypes
mNextServedView
plugins.flutter.io/webview
dev.flutter.pigeon.webview_flutter_an...
POLL_FAILED
java.lang.String
GET_WEB_VIEW_RENDERER
resizeDown
TextInput.setClient
dev.flutter.pigeon.webview_flutter_an...
ADDING
REQUESTED_WITH_HEADER_ALLOW_LIST
framework
dev.flutter.pigeon.webview_flutter_an...
io.flutter.plugins.sharedpreferences....
android.intent.action.TIME_TICK
:dev
New
mMainThread
android.intent.action.TIME_SET
indicate_encryption_required
UNLOCK_FAIL
pin
android.widget.CheckBox
translateY
translateX
TextView
setEpicenterBounds
ImageView
no_permissions
HapticFeedback.vibrate
repeatCount
void
flutter/restoration
onUserLeaveHint
mStableInsets
_cur
mOverlapAnchor
OFFER_SUCCESS
PROXY_OVERRIDE:3
GATT_INTERNAL_ERROR
BYTES_LIST
_id
basic
kotlin.Throwable
sidecarDisplayFeatures
androidx.datastore.preferences.core.P...
dev.flutter.pigeon.shared_preferences...
parkedWorkersStack
turnOff
android.permission.GET_ACCOUNTS
RINGTONES
PHONE
systemNavigationBarColor
cause
displayCutout
PlatformPlugin
SFIXED64_LIST_PACKED
dev.flutter.pigeon.webview_flutter_an...
write_without_response
notify_encryption_required
kotlin.Annotation
OnCharacteristicWritten
android.permission.NEARBY_WIFI_DEVICES
LANDSCAPE_LEFT
direction
dev.flutter.pigeon.shared_preferences...
android.intent.action.SEARCH
BITMAP
android.permission.WRITE_CALL_LOG
android.bluetooth.adapter.action.REQU...
newConfig
io.flutter.plugins.sharedpreferences....
Array
ATTACH
activities
bonding
WindowServer
setValue
android.permission.CAMERA
UNKNOWN
/proc/self/fd/
ActivityFilter
tx_phy
GATT_INSUFFICIENT_AUTHORIZATION
RestrictedApi
StateFlow.kt
fileName
getByte
accessibility
endColor
UINT32
UNACCEPTABLE_CONNECTION_PARAMETERS
GATT_INSUFFICIENT_ENCRYPTION
centerColor
password
kotlinx.coroutines.scheduler.keep.ali...
VGhpcyBpcyB0aGUgcHJlZml4IGZvciBEb3VibGUu
ALGORITHMIC_DARKENING
NO_TARGET
RequiresFeature
state
CompanionObject
secondaryActivityStack.activities
element
zoomIn
ACTION_SCROLL_DOWN
Reduce.kt
android.view.ViewRootImpl
Destroying.
input
android.bluetooth.adapter.action.REQU...
SupportMenuInflater
dev.flutter.pigeon.webview_flutter_an...
dev.flutter.pigeon.webview_flutter_an...
anim
getHorizontallyScrolling
WEBVIEW_INTEGRITY_API_STATUS
Spinner
copyMemory
JavascriptInterface
dev.flutter.pigeon.PathProviderApi.ge...
CUSTOM_ACTION
android.bluetooth.device.action.FOUND
readAndInitOrPropagateFailure
android.support.v13.view.inputmethod....
STANDARD
cached_engine_id
android.permission.BODY_SENSORS_BACKG...
connectGatt
setRemoveOnCancelPolicy
MUSIC
io.flutter.plugins.sharedpreferences....
NEW_BUILDER
android.permission.READ_MEDIA_IMAGES
forbidden
gradient
IS_LIVE_REGION
checkOpNoThrow
refresh
dev.flutter.pigeon.webview_flutter_an...
windowToken
onTrimMemory
services
getSourceNodeId
androidx.lifecycle.ViewModelProvider....
ArrayBuffer
chars
arrayIndexScale
ROLE_CHANGE_NOT_ALLOWED
kotlinx.coroutines.android.AndroidDis...
performStopActivity
flutter/backgesture
SplitPlaceholderRule
OnTurnOnResponse
ALERT
source
LocalizationChannel
AUTHENTICATION_FAILURE
UNKNOWN_CONNECTION_IDENTIFIER
java.lang.Short
GATT_INVALID_OFFSET
dev.flutter.pigeon.webview_flutter_an...
DIFFERENT_TRANSACTION_COLLISION
android.widget.Button
has
androidx.view.accessibility.Accessibi...
REPLACE
SCALAR
markState
AvdcInflateDelegate
addressCity
INTEGER
UINT32_LIST_PACKED
androidx.datastore.preferences.protob...
search_suggest_query
UINT64
NAME
FragmentManager
bondDevice
dev.flutter.pigeon.webview_flutter_an...
phoneNumber
SECURE_SIMPLE_PAIRING_NOT_SUPPORTED
composingBase
peekByte
RESERVED_SLOT_VIOLATION
MESSAGE_LIST
dev.flutter.pigeon.webview_flutter_an...
isTaken
font_variation_settings
FORCE_DARK
onRequestPermissionsResult
platformViewId
EDGE_TO_EDGE
LEAN_BACK
_isTerminated
libcore.io.Memory
tint
SEALED
LifecycleChannel
CREATED
PermissionHandler.ServiceManager
SHOW
kGamepad
io.flutter.plugins.sharedpreferences....
OnServicesReset
SeekBar
rotation
callbackWrapper
getPosture
pair
BOOL_LIST
getLayoutDirection
android_check_location_services
endY
endX
DATETIME
BOOL
PASTE
short
startY
startX
android.bluetooth.device.extra.PAIRIN...
IS_HEADER
COMPLETING_RETRY
VISIBLE_PASSWORD
IS_FOCUSABLE
android.bluetooth.adapter.action.DISC...
OrBuilderList
TextInput.clearClient
SCROLL_RIGHT
textCapitalization
PACKET_TOO_LONG
moduleName
android.widget.ImageView
android.widget.RadioButton
mDisplayListeners
ACCESSIBILITY_CLICKABLE_SPAN_ID
put
ACTION_PAGE_LEFT
TextInputType.text
embeddingCallback
in_progress
pokeLong
shouldShowRequestPermissionRationale
font_ttc_index
SCROLL_LEFT
options
HapticFeedbackType.heavyImpact
ACTION_SHOW_ON_SCREEN
InputConnectionAdaptor
flutter/platform
TEXTURE_WITH_HYBRID_FALLBACK
CheckedTextView
SERVICE_WORKER_CONTENT_ACCESS
POSTURE_HALF_OPENED
POSTAL_ADDRESS
pairingKey
with_remote_ids
viewFocused
robolectric
dev.flutter.pigeon.shared_preferences...
strokeLineJoin
light
RETRY_ATOMIC
android.settings.BLUETOOTH_SETTINGS
uninstallDeferredComponent
java.lang.Throwable
FragmentManager:
handleUpdate
$splitsWithActivity
PrivateApi
SystemSound.play
ImageReaderSurfaceProducer
getPhySupport
MissingSuperCall
unknown
GATT_ILLEGAL_PARAMETER
android.widget.SeekBar
android.intent.action.RUN
android.permission.ACCESS_NOTIFICATIO...
INITIALIZED
android.permission.REQUEST_IGNORE_BAT...
android.support.v13.view.inputmethod....
KeyEventChannel
producerIndex
REMOVE
flutter/settings
addressLocality
discoverServices
value.stringSet.stringsList
GATT_ATTR_NOT_FOUND
USER_AGENT_METADATA
dev.flutter.pigeon.webview_flutter_an...
ensurePermissions
callbackInterface
DeviceOrientation.landscapeLeft
FIXED32_LIST
.immediate
TextInputAction.unspecified
with_msd
char
IMMERSIVE
TAP
SCAN_FAILED_OUT_OF_HARDWARE_RESOURCES
dev.flutter.pigeon.webview_flutter_an...
listen
Bytes
disconnected
uimode
KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS
TextInputAction.commitContent
inputType
BOTH
group
java.lang.Cloneable
turnOn
isSupported
android.speech.extra.RESULTS_PENDINGI...
GeneratedPluginRegistrant
DOCUMENT_START_SCRIPT:1
io.flutter.embedding.android.EnableIm...
WEB_VIEW_RENDERER_TERMINATE
MULTI_PROFILE
WrongConstant
androidx.core.view.inputmethod.InputC...
firstOrNull
setWindowLayoutType
android.permission.ACTIVITY_RECOGNITION
onAttachedToEngine
setLocale
Clipboard.setData
onDetachedFromActivity
TextInputType.phone
TextInput.sendAppPrivateCommand
turningOff
GET_COOKIE_INFO
INT32_LIST_PACKED
consumerIndex
point
descriptors
_removedRef
bond_error
alias
dev.flutter.pigeon.shared_preferences...
SINT64_LIST_PACKED
ATTRIBUTION_REGISTRATION_BEHAVIOR
kotlin.String.Companion
SCO_INTERVAL_REJECTED
ACTION_SHOW_TOOLTIP
jClass
TextInput.show
value_
addFontFromAssetManager
doBeforeTextChanged
android.bluetooth.adapter.extra.STATE
primaryActivityName.className
VECTOR
flutter_bluetooth_serial/discovery
windowMetrics
SCAN_FAILED_APPLICATION_REGISTRATION_...
dev.flutter.pigeon.webview_flutter_an...
systemNavigationBarDividerColor
CLIP_PATH
mAttachInfo
value.string
suggestions
$splitRule
mResourcesImpl
resizeColumn
sidecarCompat
java.specification.version
plugins
kotlin.Cloneable
IS_FOCUSED
PlatformViewsController
COARSE_CLOCK_ADJUSTMENT_REJECTED
path
ReceiveQueued
removeBond
Button
propertyValuesHolder
kotlin.reflect.jvm.internal.Reflectio...
PARKING
FOLD
addObserver
android:target_req_state
dev.flutter.pigeon.webview_flutter_an...
SFIXED32_LIST
ON_ANY
TwilightManager
makeOptionalFitsSystemWindows
SystemNavigator.setFrameworkHandlesBack
SINT32
ON_PAUSE
route
interpolator
DataMigrationInitializer.kt
domain
viewType
editingValue
invokeSuspend
GET_WEB_VIEW_CLIENT
UNKNOWN_COMMAND
SidecarCompat
DROP_LATEST
transparent
REMOVING
background_mode
NAMES_ROUTE
defaultDisplay
android.permission.SEND_SMS
clearFocus
transition
DATA_DIRECTORY_BASE_PATH
hintText
LOCALE
InlinedApi
personNamePrefix
TIP
android.permission.BLUETOOTH
androidx.core.view.inputmethod.InputC...
feature.rect
2A05
flutter_blue_plus/methods
missingDelimiterValue
dev.flutter.pigeon.shared_preferences...
listener
INQUIRY_RESPONSE_TOO_LARGE
BanParcelableUsage
secondaryActivityName
AwaitContinuation
android:menu:expandedactionview
free_form
kotlin.Boolean
manufacturer_data
androidx.transition.FragmentTransitio...
isOn
primary_service_uuid
_queue
setSidecarCallback
List
reply
info
DORMANT
HYBRID_ONLY
android.permission.READ_MEDIA_AUDIO
kHdmi
android.permission.RECORD_AUDIO
dev.flutter.pigeon.PathProviderApi.ge...
TextInputType.name
onDetachedFromActivityForConfigChanges
navigation_bar_height
java.lang.annotation.Annotation
android.permission.BLUETOOTH_CONNECT
FlutterImageView
font_weight
android.permission.ACCESS_MEDIA_LOCATION
TERMINATED
bitField0_
ACTION_SET_SELECTION
flutter/navigation
Localization.getStringResource
SAFE_BROWSING_PRIVACY_POLICY_URL
primaryActivity
secondaryActivity
mButtonDrawable
kotlin.collections.Map
duration
GATT_ATTR_NOT_LONG
ProcessText.queryTextActions
cached_engine_group_id
ListPopupWindow
updateBackGestureProgress
alpha
TRACING_CONTROLLER_BASIC_USAGE
java.lang.Boolean
selector
io.flutter.plugins.sharedpreferences....
continuation
DATA_DIRECTORY_SUFFIX
strings_
pathData
CONNECTION_REJECTED_SECURITY_REASONS
DeviceOrientation.landscapeRight
IconCompat
GET_MEMOIZED_IS_INITIALIZED
dev.flutter.pigeon.webview_flutter_an...
keydown
activateSystemCursor
trimPathStart
flutter/accessibility
resize
TextEditingDelta
DEFAULT
strokeMiterLimit
SERVICE_WORKER_CACHE_MODE
DROP_OLDEST
splitInfoList
android.permission.WRITE_CALENDAR
invalid_argument
lastScheduledTask
viewportHeight
birthdayMonth
dev.flutter.pigeon.webview_flutter_an...
SFIXED64_LIST
autocorrect
inFlightTasks
androidx.activity.result.contract.ext...
userdebug
GET_WEB_CHROME_CLIENT
HapticFeedbackType.selectionClick
endIndex
androidx.view.accessibility.Accessibi...
action
text
SENTENCES
EditText
TextInput.finishAutofillContext
io.flutter.embedding.android.EnableOp...
arch_disk_io_%d
signed
PlatformChannel
getLayoutAlignment
FlutterView
PLAIN_TEXT
dev.flutter.pigeon.webview_flutter_an...
applicationContext
getResId
MOVE_CURSOR_FORWARD_BY_WORD
androidx.datastore.preferences.protob...
AccessibilityChannel
fullStreetAddress
GET_DEFAULT_INSTANCE
LMP_OR_LL_ERROR_TRANS_COLLISION
ACTION_CLEAR_SELECTION
REPEATED_ATTEMPTS
secondaryActivityStack
notify
file
BYTE_STRING
stream
PAIRING_NOT_ALLOWED
rx_phy
WEB_MESSAGE_ARRAY_BUFFER
CancellableContinuation
openSettings
GATT_NOT_ENCRYPTED
HIDE
ACTION_PAGE_RIGHT
androidx.datastore.preferences.core.P...
onComplete
EmbeddingCompat
menu
uri
androidx.core.view.inputmethod.InputC...
getLong
TextInputAction.done
dev.flutter.pigeon.webview_flutter_an...
CREATE_WEB_MESSAGE_CHANNEL
OPERATION_CANCELLED_BY_HOST
ACTION_HIDE_TOOLTIP
kotlin.internal.JRE8PlatformImplement...
onSaveInstanceState
io.flutter.EntrypointUri
VIRTUAL_DISPLAY_PLATFORM_VIEW
IS_SLIDER
newIndent
dev.flutter.pigeon.webview_flutter_an...
resizeUpLeftDownRight
KeyboardManager
AsldcInflateDelegate
RESUMED
KEY_COMPONENT_ACTIVITY_PENDING_RESULT
SpellCheckChannel
android.permission.READ_CALENDAR
main
mDrawableCache
activityFilters
commitBackGesture
UnknownNullness
mVisibleInsets
MissingPermission
birthDateMonth
CONNECTION_REJECTED_LIMITED_RESOURCES
ListenableEditingState
separator
android_scan_mode
personMiddleName
GATT_REQUEST_NOT_SUPPORTED
phy_options
null
font_italic
background
true
androidx.datastore.preferences.protob...
SAFE_BROWSING_ENABLE
dispose
phoneNational
characteristic_uuid
UNDISPATCHED
objectAnimator
LambdaLast
onMtuChanged:
LINK_KEY_CANNOT_BE_EXCHANGED
CompoundButtonCompat
featureBounds
android.webkit.
transformer
GATT_SERVICE_STARTED
ACTION_SCROLL_RIGHT
CONNECTION_FAILED_ESTABLISHMENT
statusBarIconBrightness
ERROR_BLUETOOTH_NOT_ENABLED
dcim
peekLong
UINT32_LIST
android.permission.BLUETOOTH_ADVERTISE
asyncTraceEnd
transform
STARTED
android:view_registry_state
STARTUP_FEATURE_SET_DATA_DIRECTORY_SU...
GATT_DB_FULL
MenuItemCompat
vm_snapshot_data
Clipboard.getData
SCAN_FAILED_INTERNAL_ERROR
android.bluetooth.adapter.extra.DISCO...
ResourceManagerInternal
dev.flutter.pigeon.shared_preferences...
Trace
ACCESSIBLE_NAVIGATION
error_string
ACTVAutoSizeHelper
getAdapterName
bytes
androidx.datastore.preferences.protob...
Completed
callback
UNINITIALIZED
RESUME_TOKEN
mOnKeyListener
android.permission.READ_EXTERNAL_STORAGE
onDescriptorRead:
ACTION_PASTE
properties
android.graphics.FontFamily
ProcessText.processTextAction
RestrictedAPI
SINT32_LIST
io.flutter.plugins.sharedpreferences....
PAIRING_WITH_UNIT_KEY_NOT_SUPPORTED
TextInput.hide
primaryActivityStack
OFF_SCREEN_PRERASTER
telephoneNumberNational
character
dev.flutter.pigeon.PathProviderApi.ge...
Unknown
java.lang.Long
metaState
valueType
INTERNAL
TRACE_TAG_APP
SERVICE_WORKER_SHOULD_INTERCEPT_REQUEST
height
onCharacteristicRead:
PACKED_VECTOR
CUT
onServicesDiscovered:
_decision
ENCRYPTION_MODE_NOT_ACCEPTABLE
kotlinx.coroutines.flow.AbstractFlow
REMOTE_DEVICE_TERMINATED_CONNECTION_L...
io.flutter.plugins.sharedpreferences....
input_method
oldText
Clipboard.hasStrings
dev.flutter.pigeon.shared_preferences...
dev.flutter.pigeon.webview_flutter_an...
setCurrentState
StartIntentSenderForResult
SAFE_BROWSING_WHITELIST
kotlin.Function
kJoystick
long
DOCUMENT_START_SCRIPT
startBackGesture
kotlinx.coroutines.channels.defaultBu...
ACTION_PAGE_DOWN
classes_to_restore
activity.windowManager.currentWindowM...
ViewConstructor
getBoolean
dev.flutter.pigeon.webview_flutter_an...
ROLE_SWITCH_PENDING
FIXED64_LIST
androidx.datastore.core.SingleProcess...
android.type.verbatim
secondaryActivityName.packageName
WORDS
androidx.core.view.inputmethod.Editor...
dev.flutter.pigeon.webview_flutter_an...
getAdapterState
systemNavigationBarIconBrightness
contextMenu
propertyName
MAC_CONNECTION_FAILED
io.flutter.plugins.sharedpreferences....
progress
STRING
CONNECTION_TERMINATED_MIC_FAILURE
autofill
TextCapitalization.none
operation
android.widget.EditText
SCO_AIR_MODE_REJECTED
NO_DECISION
dev.flutter.pigeon.webview_flutter_an...
postalCode
dev.flutter.pigeon.webview_flutter_an...
Operations:
DrawableDelegate
TextInput.setEditingState
WEB_MESSAGE_PORT_SET_MESSAGE_CALLBACK
ON_START
KEY_COMPONENT_ACTIVITY_LAUNCHED_KEYS
WEB_VIEW_RENDERER_CLIENT_BASIC_USAGE
windowMetrics.bounds
birthDateFull
2902
kotlinx.coroutines.io.parallelism
NUMBER
ResourcesCompat
LIST_EMPTY
android.resource
TypefaceCompatApi21Impl
collection
WARNING
BOOLEAN
android.view.DisplayInfo
RECEIVE_HTTP_ERROR
_next
isAvailable
StartActivityForResult
io.flutter.plugins.sharedpreferences....
rule
STARTUP_FEATURE_SET_DIRECTORY_BASE_PATH
downloads
textservices
dev.flutter.pigeon.webview_flutter_an...
setClipToScreenEnabled
pokeByte
getMaxAvailableHeight
mask
kotlinx.coroutines.test.internal.Test...
characteristics
WrappedDrawableApi21
CUTOUT
creditCardExpirationYear
MULTI_PROCESS
SystemUiMode.leanBack
binaryMessenger
dev.flutter.pigeon.PathProviderApi.ge...
flutterRestart
splitRules
ON_CLOSE_HANDLER_INVOKED
dev.flutter.pigeon.shared_preferences...
java.lang.Object
SystemChrome.setSystemUIChangeListener
base
disconnect
TextInput.setPlatformViewClient
extendedPostalCode
androidx.core.view.inputmethod.InputC...
prefix
ByteString
binding
kKeyboard
zoomOut
movies
pairingRequestHandlingEnable
getViewRootImpl
io.flutter.plugins.sharedpreferences....
delimiter
enable
PrivateConstructorForUtilityClass
tel:123123
NONE
primaryActivityStack.activities
onReliableWriteCompleted:
LOG
hints
onWindowLayoutChangeListenerAdded
java.util.Set
keyup
androidx.datastore.core.DataMigration...
onCharacteristicChanged:
bond_state
result_code
brieflyShowPassword
SimpleActor.kt
newDeviceState
broadcast
kotlinx.coroutines.scheduler
gender
ActivityResultRegistry
createFromFamiliesWithDefault
isConnected
resizeRow
android.webkit.WebViewFactory
usesVirtualDisplay
pictures
HAS_EXPANDED_STATE
NO_THREAD_ELEMENTS
tracker
FLAT
getDisplayInfo
DOUBLE
CACHE_DIRECTORY_BASE_PATH
SOURCE_INPUT_METHOD
BanUncheckedReflection
include
resizeDownRight
GATT_INSUFFICIENT_AUTHENTICATION
WEBVIEW_MEDIA_INTEGRITY_API_STATUS
newLayout
DARK
IS_READ_ONLY
COPY
PENDING
setPreferredPhy
TYPE0_SUBMAP_NOT_DEFINED
nullLayouts
SCAN_FAILED_SCANNING_TOO_FREQUENTLY
appearance
ROLE_SWITCH_FAILED
java.lang.Comparable
ALARMS
ACTION_SET_TEXT
msg
android.widget.Switch
resizeUpRightDownLeft
REMOTE_DEVICE_TERMINATED_CONNECTION_P...
isProjected
float
OP_SET_MAX_LIFECYCLE
IS_BUTTON
postalAddressExtended
SINT64
java.lang.Enum
requestEnable
MultiAutoCompleteTextView
enableIMEPersonalizedLearning
familyName
DETACHED
android.permission.POST_NOTIFICATIONS
adv_name
TextInputType.datetime
action_msg
TextInputAction.go
android.speech.extra.PROMPT
offset
dev.flutter.pigeon.webview_flutter_an...
SystemChrome.setApplicationSwitcherDe...
IS_HIDDEN
stopScan
failed
mtu
SHOULD_OVERRIDE_WITH_REDIRECTS
dev.flutter.pigeon.webview_flutter_an...
sequence
keyCode
android.permission.SCHEDULE_EXACT_ALARM
DATA
UTF8
androidx.core.view.inputmethod.Editor...
file_id
didGainFocus
flutter/localization
HORIZONTAL
putObject
FlutterRenderer
androidx.datastore.preferences.protob...
.font
ruleComponent.packageName
ACTION_CUT
OnAdapterStateChanged
Brightness.light
LTR
error
kotlin.Byte
network
RequestPermissions
RECEIVE_WEB_RESOURCE_ERROR
array
postfix
value
opaque
REUSABLE_CLAIMED
0x%08x
remote_id
dart_entrypoint_args
android.hardware.telephony
int
bluetooth
ImageReaderPlatformViewRenderTarget
com.google.android.inputmethod.latin
AccessibilityBridge
exception
STRING_LIST
cancelDiscovery
TextInput.requestAutofill
suggest_intent_data
androidx.core.view.extra.INPUT_CONTEN...
String
deviceId
wlan0
music
androidx.datastore.core.SimpleActor$o...
verticalText
FlutterSharedPreferences
android.util.LongArray
androidx.view.accessibility.Accessibi...
android.bluetooth.device.extra.PAIRIN...
getUncaughtExceptionPreHandler
onBackPressed
adapter
disconnect_reason_string
LANDSCAPE_RIGHT
ERROR_GATT_WRITE_NOT_ALLOWED
LEFT
LMP_PDU_NOT_ALLOWED
_handled
givenName
ActivityRule
androidx.datastore.core.DataMigration...
androidx.datastore.core.SingleProcess...
kotlin.jvm.functions.
WindowInfoTrackerImpl.kt
extended_properties
onMenuKeyEvent
TextInputClient.updateEditingStateWit...
android.permission.READ_CONTACTS
kotlin.internal.JRE7PlatformImplement...
le_coded
DISABLED_ACTION_MODE_MENU_ITEMS
DECREASE
PORTRAIT_DOWN
android.settings.action.MANAGE_OVERLA...
touchOffset
android.intent.extra.PROCESS_TEXT_REA...
dev.flutter.pigeon.webview_flutter_an...
VectorDrawableCompat
libapp.so
fillColor
MESSAGE
android.bluetooth.device.action.PAIRI...
RestorationChannel
middleInitial
PHYSICAL_DISPLAY_PLATFORM_VIEW
codePoint
SystemUiOverlay.top
WEB_RESOURCE_ERROR_GET_CODE
IS_CHECK_STATE_MIXED
displayFeatures
pairingRequestHandlingDisable
SwitchIntDef
SAFE_BROWSING_ALLOWLIST
androidx.lifecycle.BundlableSavedStat...
package:
this$0
Loaders:
onNewIntent
PROXY_OVERRIDE
acc
flutter_assets
connectedCount
getName
TextInputClient.performPrivateCommand
MUTE_AUDIO
ack
currentIndex
startColor
with_names
SFIXED32_LIST_PACKED
addSuppressed
CSLCompat
ACTION_SET_PROGRESS
RESULT_OK
androidx.core.view.inputmethod.Editor...
activityComponent.className
addressCountry
manufacturer_id
CONNECTION_ALREADY_EXISTS
trimPathEnd
LIGHT
RadioButton
DID_LOSE_ACCESSIBILITY_FOCUS
TypefaceCompatUtil
NEW_MUTABLE_INSTANCE
mUnthemedEntries
phoneCountryCode
telephoneNumberCountryCode
CheckBox
error_code
deviceState
decimal
ERROR_UNKNOWN
GONE
getSystemDevices
strokeWidth
le_2M
dev.flutter.pigeon.webview_flutter_an...
CONTROLLER_BUSY
ROOT
TextInput.setEditableSizeAndTransform
ACTION_CLEAR_FOCUS
ClassVerificationFailure
getDouble
ACTION_SCROLL_BACKWARD
dev.flutter.pigeon.webview_flutter_an...
androidx.core.view.inputmethod.InputC...
android.permission.WRITE_EXTERNAL_STO...
FIXED32
TooltipPopup
android.graphics.drawable.VectorDrawable
scope
SET_SELECTION
strokeLineCap
INVALID_LMP_OR_LL_PARAMETERS
io.flutter.plugins.sharedpreferences....
ACTION_SCROLL_UP
io.flutter.embedding.android.OldGenHe...
eng
BITMAP_MASKABLE
ENTERPRISE_AUTHENTICATION_APP_LINK_PO...
GATT_INVALID_PDU
IS_OBSCURED
label
message
FORCE_DARK_BEHAVIOR
scanCode
FlutterJNI
androidx.datastore.core.SingleProcess...
android:support:fragments
UNLOCKED
setDisplayFeatures
HapticFeedbackType.lightImpact
creditCardExpirationDay
SAFE_BROWSING_RESPONSE_PROCEED
username
decorate
centerY
setExclusiveCheckable
centerX
UNDEFINED
requestPermissions
ACTION_CONTEXT_CLICK
messageInfoFactory
SplitPairRule
IS_TEXT_FIELD
property
kotlinx.coroutines.internal.StackTrac...
android.settings.NOTIFICATION_POLICY_...
setOptions
GET_VARIATIONS_HEADER
TextInputType.none
kotlin.coroutines.jvm.internal.a
UINT64_LIST_PACKED
TextInputAction.none
secondaryActivityName.className
android.content.res.ThemedResourceCache
getNextMatch
binding.applicationContext
datastore/
putFloat
CONNECTION_LIMIT_EXCEEDED
onUndeliveredElement
CLIP_RECT
MAP
io.flutter.Entrypoint
.xml
WEB_RESOURCE_ERROR_GET_DESCRIPTION
other
FORCE_DARK_STRATEGY
android.intent.extra.TEXT
getDeviceBondState
IS_LINK
putDouble
cell
safeCast
URI
FlutterTextureView
URL
suggest_text_1
dev.flutter.pigeon.webview_flutter_an...
suggest_text_2
androidx.view.accessibility.Accessibi...
kotlin.Long
auto_connect
androidx.view.accessibility.Accessibi...
Completing
SAVE
NOTIFICATIONS
addressRegion
GATT_BUSY
PROXY_OVERRIDE_REVERSE_BYPASS
android.settings.APPLICATION_DETAILS_...
_resumed
SOURCE_APP
TextInputType.multiline
getChildId
android.permission.RECEIVE_MMS
FIXED64
PRIVATE
LoaderManager
UNSUPPORTED_PARAMETER_VALUE
GATT_AUTH_FAIL
java.util.concurrent.ForkJoinPool
NavUtils
LIGHT_IMPACT
DESTROYED
mListener
allow_long_write
SystemChrome.setEnabledSystemUIOverlays
selectionExtent
MouseCursorChannel
ATOMIC
kotlin.collections.Collection
write_type
TextInputAction.send
scaleX
ERROR_BLUETOOTH_NOT_ALLOWED
scaleY
readAndInit
onStart
buffer
noDrop
$activityFilters
flutter/keydata
read
memoryPressure
tx_power_level
touch
MenuItemWrapper
CommonPool
onResume
android.permission.MANAGE_EXTERNAL_ST...
java.util.List
hybrid
kotlinx.coroutines.flow.FlowKt__ReduceKt
UNKNOWN_ADVERTISING_IDENTIFIER
dev.flutter.pigeon.webview_flutter_an...
UNEXPECTED:
kotlin.Int
VOID
collect
BackGestureChannel
GATT_UNLIKELY
connect_error
android.view.
OP_POST_NOTIFICATION
FLOAT
android.permission.CALL_PHONE
handleLifecycleEvent
IS_IMAGE
logic_error
COMPLETING_ALREADY
serializer
java.lang.Iterable
androidx.appcompat.widget.LinearLayou...
readException
rect
kotlinx.coroutines.DefaultExecutor.ke...
INVERT_COLORS
synchronizeToNativeViewHierarchy
packageName
info.displayFeatures
android.permission.RECEIVE_WAP_PUSH
arrayBaseOffset
androidx.core.view.inputmethod.InputC...
windowConfiguration
resizeUpRight
dev.flutter.pigeon.webview_flutter_an...
dev.flutter.pigeon.webview_flutter_an...
android:user_visible_hint
readDescriptor
flutter/lifecycle
TextInputType.number
layout_inflater
WEB_RESOURCE_REQUEST_IS_REDIRECT
WEB_MESSAGE_CALLBACK_ON_MESSAGE
getParentNodeId
FlutterBluePlugin
android.permission.READ_PHONE_NUMBERS
androidx.datastore.core.DataMigration...
getPoolSize
descriptor_uuid
suggest_intent_extra_data
SystemChrome.setEnabledSystemUIMode
getAppBounds
PermissionHandler.PermissionManager
permissions_handler
ringtones
suggest_flags
android.widget.HorizontalScrollView
INCREASE
androidx.datastore.preferences.protob...
OnReadRssi
jar
ERROR
cache
creditCardExpirationMonth
android.permission.BLUETOOTH_SCAN
androidx.appcompat.app.ActionBar$Tab
deltaText
BUILD_MESSAGE_INFO
dev.flutter.pigeon.webview_flutter_an...
CONNECTION_TERMINATED_BY_LOCAL_HOST
dev.flutter.pigeon.shared_preferences...
REMOVE_FROZEN
TaskStackBuilder
succeeded
.tmp
peekInt
getBondState
PathParser
PROTO2
PROTO3
POSTURE_FLAT
androidx.savedstate.Restarter
BanConcurrentHashMap
FEATURE_NOT_SUPPORTED
kotlin.collections.ListIterator
oemFeature.bounds
GROUP
putByte
getKeyboardState
TextInputClient.updateEditingStateWit...
delimiters
objectFieldOffset
PROTECTED
flutter_bluetooth_serial/state
ACTION_SELECT
newLayoutInfo
viewportWidth
blockingTasksInBuffer
androidx.datastore.core.SingleProcess...
service_uuid
android.permission.READ_CALL_LOG
streetAddress
dev.flutter.pigeon.webview_flutter_an...
kotlin.Char
flutter/isolate
ACTION_ARGUMENT_EXTEND_SELECTION_BOOLEAN
grab
Share.kt
ERROR_PROFILE_SERVICE_NOT_BOUND
animator
GATT_FAILURE
dimen
documents
java.util.ListIterator
DefaultDispatcher
user_accepted
kotlin.Double
AppCompatDelegate
onActivityResult
dev.flutter.pigeon.PathProviderApi.ge...
view
allScroll
ERROR_DEVICE_NOT_BONDED
SystemChrome.systemUIChange
NewApi
ViewCompat
GATT_PENDING
primaryActivityName
suggest_intent_query
includeSubdomains
android.support.v13.view.inputmethod....
overlay
androidx.core.view.inputmethod.Editor...
android.intent.action.CALL
creditCardSecurityCode
parentMetrics
UINT64_LIST
FULL
finalException
flutter
startOffset
dev.flutter.pigeon.webview_flutter_an...
name
Class
fields
NestedScrollView
DartExecutor
SCAN_FAILED_FEATURE_UNSUPPORTED
prev_state
getDescriptor
FAILURE_REGISTERING_CLIENT
N/A
keymap
FlutterLoader
string
bool
deltaStart
kotlin.coroutines.jvm.internal.BaseCo...
android
java.lang.module.ModuleDescriptor
description
nameSuffix
VISIBLE
android.support.v13.view.inputmethod....
status_bar_height
textScaleFactor
QOS_REJECTED
namePrefix
OnMtuChanged
platform_name
VERBOSE
SharedPreferencesPlugin
thisRef
PARAMETER_OUT_OF_RANGE
VALUE_NOT_SET
START_SAFE_BROWSING
android:view_state
flutter.baseflow.com/permissions/methods
io.flutter.embedding.android.NormalTheme
Dispatchers.Main
SOURCE_CLIPBOARD
FlutterSurfaceView
REQUESTED_QOS_NOT_SUPPORTED
Cancelled
getEmptyRegistry
RatingBar
target
enableSuggestions
primitiveFqNames.values
IS_MULTILINE
middleName
window
personFamilyName
Cancelling
CLIP_RRECT
VdcInflateDelegate
isDiscovering
OnBondStateChanged
tileMode
hybridFallback
oemFeature
getBounds
androidx:appcompat
EmptyCoroutineContext
CPU_ACQUIRED
ACTION_ARGUMENT_SELECTION_END_INT
disconnect_reason_code
clearGattCache
propertyYName
TextInputAction.search
windowBackend
SINT64_LIST
SFIXED64
item
dart_entrypoint
android.bluetooth.adapter.action.STAT...
kotlinx.coroutines.scheduler.core.poo...
kotlinx.coroutines.scheduler.max.pool...
android.permission.ACCESS_BACKGROUND_...
double
KEY_COMPONENT_ACTIVITY_RANDOM_OBJECT
GET_PARSER
newPassword
smsOTPCode
SystemChannel
flutter_deeplinking_enabled
INVISIBLE
HEAVY_IMPACT
kotlin.Short
phone
primaryActivityName.packageName
bounds
PUBLIC
dev.flutter.pigeon.webview_flutter_an...
android.settings.MANAGE_UNKNOWN_APP_S...
getDisplayFeatures
BLOCKING
consumeMessage
BOOL_LIST_PACKED
BOLD_TEXT
Dispatchers.Unconfined
WindowInfoTrackerCallbackAdapter.kt
flags
ViewConfigCompat
onPause
SystemUiOverlay.bottom
enabled
android.permission.ACCESS_FINE_LOCATION
LAZY
mContentInsets
requestDiscoverable
PlatformViewWrapper
mToken
setDirection
ENUM_LIST
java.util.Map$Entry
SendQueued
display
SpellCheck.initiateSpellCheck
composingExtent
androidx.datastore.core.SingleProcess...
service_uuids
onConnectionStateChange:
birthDateDay
dev.flutter.pigeon.webview_flutter_an...
ASCENDING
libflutter.so
width
GATT_ERROR
PORTRAIT_UP
SFIXED32
BYTES
dev.flutter.pigeon.webview_flutter_an...
kotlinx.coroutines.fast.service.loader
VISUAL_STATE_CALLBACK
dev.flutter.pigeon.webview_flutter_an...
GATT_INVALID_CFG
kotlin.Any
KEY_COMPONENT_ACTIVITY_REGISTERED_RCS
Brightness.dark
ThemeUtils
GATT_SUCCESS
readAndInitOrPropagateAndThrowFailure
listString
AppCompatViewInflater
selectionBase
SettingsChannel
notification
2
plainCodePoint
dev.flutter.pigeon.webview_flutter_an...
OnCharacteristicReceived
_reusableCancellableContinuation
android.view.View
UNSUPPORTED_LMP_OR_LL_PARAMETER_VALUE
java.lang.Byte
AppBundleLocaleChanges
DONE
deltaEnd
putInt
kind
SplitPairFilter
COMMAND_DISALLOWED
android.permission.REQUEST_INSTALL_PA...
_consensus
SCROLL_UP
android.support.v13.view.inputmethod....
SingleProcessDataStore.kt
CLOSED_EMPTY
kotlin.collections.Iterator
res/
job
TextInputClient.requestExistingInputS...
onScanResult
PreferenceDataStoreFactory.kt
rebase
dev.flutter.pigeon.webview_flutter_an...
SET_TEXT
GATT_READ_NOT_PERMITTED
SystemChrome.restoreSystemUIOverlays
preferencesMap
INT
SAFE_BROWSING_RESPONSE_BACK_TO_SAFETY
onCloseHandler
kotlin.Enum
fillType
PlatformViewsChannel
isDiscoverable
deltas
uniqueIdentifier
ACTION_PREVIOUS_HTML_ELEMENT
move
http://schemas.android.com/apk/res/an...
SplitRuleResolution
suggest_text_2_url
alarms
setPosture
ensureImeVisible
android.permission.ANSWER_PHONE_CALLS
splitInfo.primaryActivityStack
androidx.lifecycle.savedstate.vm.tag
WindowInsetsCompat
android.intent.action.PROCESS_TEXT
PopupWindowCompatApi21
GATT_WRITE_NOT_PERMITTED
INT32_LIST
TEXTURE_WITH_VIRTUAL_FALLBACK
STARTUP_FEATURE_SET_DIRECTORY_BASE_PATHS
mIsChildViewEnabled
ATTRIBUTION_BEHAVIOR
android.permission.READ_PHONE_STATE
getModule
extent
gradientRadius
getPrefs
kotlinx.coroutines.scheduler.blocking...
tooltip
openAppSettings
dev.flutter.pigeon.shared_preferences...
OnDiscoveredServices
dev.flutter.pigeon.webview_flutter_an...
FEATURE_SUPPORTED
messageType
flutter/keyboard
0000
dev.flutter.pigeon.webview_flutter_an...
%02X:
systemStatusBarContrastEnforced
SAFE_BROWSING_RESPONSE_SHOW_INTERSTITIAL
androidx.lifecycle.LifecycleDispatche...
getSuppressed
DOUBLE_LIST_PACKED
OnDescriptorRead
PIN_OR_KEY_MISSING
DartMessenger
ACTION_UNKNOWN
FLAG_CONVERT_TO_PLAIN_TEXT
FlutterBluetoothSerial
HIGH_CONTRAST
androidx.datastore.core.DataMigration...
ReceiveContent
STRING_SET
MOVE_CURSOR_BACKWARD_BY_WORD
kotlinx.coroutines.scheduler.resoluti...
kotlin.Unit
web_search
birthdayDay
kotlin.jvm.internal.StringCompanionOb...
ACTION_CLICK
OFFER_FAILED
WEB_MESSAGE_PORT_POST_MESSAGE
android.speech.extra.LANGUAGE_MODEL
setName
schema
ordering
02:00:00:00:00:00
java.
createWebViewProviderFactory
resizeUpDown
SystemSoundType.click
advertisements
ImageButton
popRoute
throwableMethods
indicate
android.permission.USE_SIP
completion
android.permission.READ_MEDIA_VIDEO
with_services
ToggleButton
androidx.view.accessibility.Accessibi...
set
secondaryActivityIntent
ACTION_IME_ENTER
kUp
FRAMEWORK_CLIENT
computeFitSystemWindows
DETACH
getScaledScrollFactor
INACTIVE
activityComponent.packageName
VGhpcyBpcyB0aGUgcHJlZml4IGZvciBhIGxpc3Qu
ACTION_NEXT_AT_MOVEMENT_GRANULARITY
android.support.PARENT_ACTIVITY
font
ADD
MEDIUM_IMPACT
setTouchModal
INT64_LIST
INFO
installDeferredComponent
ruleComponent.className
androidx.view.accessibility.Accessibi...
DESCENDING
autoMirrored
configurationId
Override
DOWNLOADS
bondState
image
android.speech.extra.MAX_RESULTS
TextInputChannel
componentName.className
setLogLevel
MOVIES
Failed
preferences_
OPACITY
requestMtu
NonDisposableHandle
GATT_PREPARE_QUEUE_FULL
PODCASTS
dev.flutter.pigeon.PathProviderApi.ge...
QOS_UNACCEPTABLE_PARAMETER
SyntheticAccessor
androidx.datastore.preferences.protob...
countryName
ACTION_ACCESSIBILITY_FOCUS
inputAction
nodeId
frame
io.flutter.plugins.sharedpreferences....
ACTION_LONG_CLICK
android.bluetooth.device.extra.BOND_S...
android.speech.extra.LANGUAGE
startDiscovery
getFloat
extendedAddress
putLong
INSUFFICIENT_SECURITY
DOCUMENTS
content
timeout
ENQUEUE_FAILED
statusBarColor
VGhpcyBpcyB0aGUgcHJlZml4IGZvciBCaWdJb...
force_indications
contentCommitMimeTypes
class
DISABLE_ANIMATIONS
DeferredComponentChannel
os.arch
false
FlutterEngineCxnRegstry
startScan
workerCtl
OnDescriptorWritten
TextInputClient.updateEditingState
resizeLeft
io.flutter.embedding.android.LeakVM
pushRouteInformation
SERVICE_WORKER_BLOCK_NETWORK_LOADS
GATT_INVALID_ATTRIBUTE_LENGTH
ACTION_MOVE_WINDOW
TextInputAction.next
setInitialRoute
onSubscription
TEXT
clipboard
output
HINGE
java.lang.Character
transformAndWrite
context
$newLayoutInfo
id
birthdayYear
kDirectionalPad
isolate_snapshot_data
ENUM
SPELLOUT
surface
android.support.v13.view.inputmethod....
checkServiceStatus
_bounds
SCO_OFFSET_REJECTED
it
params
AppCompatSpinner
UNDECIDED
announce
SET_PRIMARY_NAV
TextInputType.address
getInt
TextInputType.url
kotlin.jvm.internal.
android.bluetooth.device.extra.PREVIO...
Map
MULTI_PROCESS_QUERY
telephoneNumber
UNDEFINED_0x2B
UNKNOWN_LMP_PDU
cancelBackGesture
suggest_intent_action
delegate
IS_EXPANDED
SCAN_FAILED_ALREADY_STARTED
UNDEFINED_0x33
SharedPreferencesPlugin.kt
UNDEFINED_0x31
RIGHT
corruptionHandler
placeholderIntent
mServedView
onDestroy
IS_KEYBOARD_KEY
GROUP_LIST
personNameSuffix
kotlin.jvm.functions.Function
resizeLeftRight
off
platformBrightness
splitInfo.secondaryActivityStack
android.widget.ScrollView
com.android.internal.view.menu.MenuBu...
mH
loadingUnitId
flutter/processtext
GATT_CONNECTION_CONGESTED
android.speech.action.RECOGNIZE_SPEECH
newUsername
suffix
tintMode
writeData$datastore_core
personMiddleInitial
cleartextTrafficPermitted
dev.flutter.pigeon.webview_flutter_an...
limit
android.resource://
strokeColor
io.flutter.plugins.sharedpreferences....
DID_GAIN_ACCESSIBILITY_FOCUS
kotlin.Number
pokeInt
DeviceOrientation.portraitDown
androidx.datastore.core.SingleProcess...
requestDisable
NULL
TOO_LATE_TO_CANCEL
bluetooth_unavailable
.preferences_pb
kDown
dev.flutter.pigeon.shared_preferences...
MEMORY_FULL
io.flutter.embedding.android.Impeller...
entry
grabbing
NavigationChannel
alwaysUse24HourFormat
SET_MEMOIZED_IS_INITIALIZED
androidx.datastore.preferences.protob...
TextInputType.visiblePassword
nm
birthday
p0
ComplexColorCompat
peekByteArray
addFontFromBuffer
ClickableViewAccessibility
ViewUtils
ON_OFF_SWITCH_LABELS
LOCK_FAIL
mService
ACTION_SCROLL_LEFT
activity.windowManager.maximumWindowM...
kotlin.Comparable
Limit.kt
GATT_WRONG_STATE
onReattachedToActivityForConfigChanges
consumer
show_password
onDetachedFromEngine
on
connection_priority
baseKey
nativeSpellCheckServiceDefined
REMOTE_USER_TERMINATED_CONNECTION
android.app.ActivityThread
removeDeviceBond
currentDisplay
longPress
DOUBLE_LIST
pokeByteArray
COROUTINE_SUSPENDED
DEBUG
Share.invoke
MenuItemImpl
flutter/textinput
android.widget.
_LifecycleAdapter
DCIM
AutoCompleteTextView
HAS_TOGGLED_STATE
suggest_icon_1
valueTo
suggest_icon_2
PathProviderPlugin
android.support.v13.view.inputmethod....
IS_SELECTED
createAsync
ANDROID_SPECIFIC_ERROR
sp_permission_handler_permission_was_...
.Companion
io.flutter.plugins.sharedpreferences....
freeze
EMAIL_ADDRESS
java.util.Map
TextInputPlugin
SuggestionsAdapter
drawable
getObject
ACTION_NEXT_HTML_ELEMENT
readData
BOTTOM_OVERLAYS
turningOn
GATT_NO_RESOURCES
ACTION_COLLAPSE
dev.flutter.pigeon.webview_flutter_an...
setNotifyValue
OnDetachedFromEngine
SoonBlockedPrivateApi
CONDITION_FALSE
SOURCE_DRAG_AND_DROP
SERVICE_WORKER_BASIC_USAGE
commonPool
IS_IN_MUTUALLY_EXCLUSIVE_GROUP
RESULT_CANCELED
activity
updateData
INT32
fillAlpha
dev.flutter.pigeon.webview_flutter_an...
disconnecting
RTL
ENUM_LIST_PACKED
ToolbarWidgetWrapper
SystemUiMode.immersive
HALF_OPENED
onAttachedToActivity
vector
ACTION_SCROLL_TO_POSITION
audio
$this$$receiver
ImageTextureRegistryEntry
key
email
LONG
kotlin.Enum.Companion
RESOURCE
runMigrations
kotlinx.coroutines.flow.StateFlowImpl
creditCardExpirationDate
obscureText
android:target_state
texture
ACTION_EXPAND
SELECTION_CLICK
windowMetricsCalculator
android.intent.category.DEFAULT
kotlin.Float
checkPermissionStatus
OPEN
1800
connected
initTasksList
android.speech.action.WEB_SEARCH
handled
adapter_state
resizeRight
SystemNavigator.pop
TextInputAction.newline
write_error
io.flutter.embedding.android.EnableVu...
_prev
android.os.action.POWER_SAVE_MODE_CHA...
MAX_NUM_OF_CONNECTIONS_EXCEEDED
secondaryActivity.intent
SINT32_LIST_PACKED
InstanceManager
primaryColor
rules
personGivenName
dev.flutter.pigeon.PathProviderApi.ge...
pairs
variant
io.flutter.plugins.sharedpreferences....
app_data
putBoolean
TOP_OVERLAYS
ruleComponent
Dispatchers.Default
java.util.Collection
query
componentName.packageName
HIDDEN
flutter_bluetooth_serial/read/
action_key
io.flutter.plugins.sharedpreferences....
postalAddressExtendedPostalCode
NOT_IN_STACK
TextInputAction.previous
CLICK
MULTILINE
writeDescriptor
removeObserver
android.graphics.Insets
ON_DESTROY
destination
enableDeltaModel
FLOAT_LIST
abortCreation
ViewParentCompat
android:menu:actionviewstates
flutter/mousecursor
getAddress
EMPTY
text/plain
android.intent.action.TIMEZONE_CHANGED
INT64
flutter/system
withoutResponse
BlockedPrivateApi
OnScanResponse
UNSUPPORTED_REMOTE_FEATURE
filters
HAS_IMPLICIT_SCROLLING
MenuPopupWindow
Dispatchers.Main.immediate
onWindowLayoutChangeListenerRemoved
valueFrom
REMOVED_TASK
location
getInstance
splitInfo
PAGE_TIMEOUT
ACTION_PREVIOUS_AT_MOVEMENT_GRANULARITY
android.intent.extra.PROCESS_TEXT
INT64_LIST_PACKED
Index:
none
location_mode
type
actionLabel
TextCapitalization.sentences
IMMERSIVE_STICKY
VERTICAL
appops
GATT_CMD_STARTED
fileOutputStream
HapticFeedbackType.mediumImpact
getTextDirectionHeuristic
dev.flutter.pigeon.shared_preferences...
ACTION_ARGUMENT_MOVEMENT_GRANULARITY_INT
DisplayListenerProxy
DeviceOrientation.portraitUp
method
mGlobal
android.bluetooth.device.extra.DEVICE
config_showMenuShortcutsWhenKeyboardP...
CONSUMED
systemNavigationBarContrastEnforced
bonded
GATT_UNSUPPORTED_GROUP
GATT_INSUFFICIENT_KEY_SIZE
ACTION_ARGUMENT_SELECTION_START_INT
push
dev.flutter.pigeon.webview_flutter_an...
dev.flutter.pigeon.shared_preferences...
intent_extra_data_key
continuous_updates
_state
isEnabled
SHOW_ON_SCREEN
notifications
ACTION_FOCUS
overridingDecorator
android.permission.READ_SMS
readRssi
java.lang.Double
android.permission.BODY_SENSORS
binding.binaryMessenger
range
flutter/spellcheck
dev.flutter.pigeon.webview_flutter_an...
IS_TOGGLED
out
com.android.voicemail.permission.ADD_...
feature
dev.flutter.pigeon.webview_flutter_an...
dev.flutter.pigeon.webview_flutter_an...
get
produceMigrations
dark
oneTimeCode
copy
precise
power
java.lang.Number
CHANNEL_CLASSIFICATION_NOT_SUPPORTED
suggest_intent_data_id
androidException
initializer
token
fraction
SystemChrome.setPreferredOrientations
filter
help
phoneNumberDevice
podcasts
A11yActionCompat
flutter/deferredcomponent
elements
onDescriptorWrite:
android.settings.REQUEST_IGNORE_BATTE...
strokeAlpha
connecting
sharedPreferencesDataStore
URI_MASKABLE
ON_CREATE
HAS_ENABLED_STATE
connectable
data
ON_RESUME
IS_ENABLED
HARDWARE_FAILURE
onCharacteristicWrite:
getBondedDevices
LINK_SUPERVISION_TIMEOUT
TextCapitalization.characters
asyncTraceBegin
android.permission.ACCESS_COARSE_LOCA...
android.permission.READ_MEDIA_VISUAL_...
create
TRANSFORM
service_data
Flow.kt
FIXED64_LIST_PACKED
REMOVED
tap
android.bluetooth.device.action.BOND_...
androidx.window.java.layout.WindowInf...
kotlin.jvm.internal.EnumCompanionObject
ACTION_COPY
io.flutter.InitialRoute
adapterTurnOff
controlState
transition_animation_scale
swipeEdge
Active
ActivityRecreator
postalAddress
kotlin.collections.Iterable
telephoneNumberDevice
CONNECTION_REJECTED_UNACCEPTABLE_MAC_...
files
INVALID_COMMAND_PARAMETERS
calling_package
DrawableCompat
writeCharacteristic
mChildNodeIds
kotlin.collections.Map.Entry
line
android.permission.SYSTEM_ALERT_WINDOW
dev.flutter.pigeon.webview_flutter_an...
IS_CHECKED
SERVICE_WORKER_FILE_ACCESS
kotlinName
connection_state
with_keywords
mAccessibilityDelegate
ERROR_MISSING_BLUETOOTH_CONNECT_PERMI...
authenticated_signed_writes
HOST_BUSY_PAIRING
alarm
requestConnectionPriority
kotlin.collections.Set
DiscouragedPrivateApi
ResourcesFlusher
android.intent.action.SEND
androidx.activity.result.contract.ext...
getWindowLayoutInfo
java.util.Iterator
dev.flutter.pigeon.webview_flutter_an...
TextInputClient.performAction
onWindowFocusChanged
org.robolectric.Robolectric
appcompat_skip_skip
jar:file:
ON_STOP
addressState
kotlin.CharSequence
intent
personName
AppCompatResources
fragment
boolean
getState
GATT_INVALID_HANDLE
migrations
FLOAT_LIST_PACKED
dev.flutter.pigeon.webview_flutter_an...
trimPathOffset
emit
SCROLL_DOWN
Marking integer:cancel_button_image_alpha:2131296258 used because it matches string pool constant cancel
Marking attr:listChoiceBackgroundIndicator:********** used because it matches string pool constant list
Marking attr:listChoiceIndicatorMultipleAnimated:********** used because it matches string pool constant list
Marking attr:listChoiceIndicatorSingleAnimated:********** used because it matches string pool constant list
Marking attr:listDividerAlertDialog:********** used because it matches string pool constant list
Marking attr:listItemLayout:********** used because it matches string pool constant list
Marking attr:listLayout:********** used because it matches string pool constant list
Marking attr:listMenuViewStyle:********** used because it matches string pool constant list
Marking attr:listPopupWindowStyle:********** used because it matches string pool constant list
Marking attr:listPreferredItemHeight:********** used because it matches string pool constant list
Marking attr:listPreferredItemHeightLarge:********** used because it matches string pool constant list
Marking attr:listPreferredItemHeightSmall:********** used because it matches string pool constant list
Marking attr:listPreferredItemPaddingEnd:********** used because it matches string pool constant list
Marking attr:listPreferredItemPaddingLeft:********** used because it matches string pool constant list
Marking attr:listPreferredItemPaddingRight:********** used because it matches string pool constant list
Marking attr:listPreferredItemPaddingStart:********** used because it matches string pool constant list
Marking id:listMode:2131230817 used because it matches string pool constant list
Marking id:list_item:2131230818 used because it matches string pool constant list
Marking id:locale:2131230819 used because it matches string pool constant locale
Marking id:locale:2131230819 used because it matches string pool constant locale
Marking id:top:2131230888 used because it matches string pool constant top
Marking id:top:2131230888 used because it matches string pool constant top
Marking id:topPanel:2131230889 used because it matches string pool constant top
Marking id:accessibility_action_clickable_span:2131230726 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_0:2131230727 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_1:2131230728 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_10:2131230729 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_11:2131230730 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_12:2131230731 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_13:2131230732 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_14:2131230733 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_15:2131230734 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_16:2131230735 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_17:2131230736 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_18:2131230737 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_19:2131230738 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_2:2131230739 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_20:2131230740 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_21:2131230741 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_22:2131230742 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_23:2131230743 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_24:2131230744 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_25:2131230745 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_26:2131230746 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_27:2131230747 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_28:2131230748 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_29:2131230749 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_3:2131230750 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_30:2131230751 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_31:2131230752 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_4:2131230753 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_5:2131230754 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_6:2131230755 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_7:2131230756 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_8:2131230757 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_9:2131230758 used because it matches string pool constant accessibility
Marking attr:state_above_anchor:2130903273 used because it matches string pool constant state
Marking attr:tint:2130903305 used because it matches string pool constant tint
Marking attr:tint:2130903305 used because it matches string pool constant tint
Marking attr:tintMode:2130903306 used because it matches string pool constant tint
Marking attr:shortcutMatchRequired:2130903258 used because it matches string pool constant short
Marking id:shortcut:2131230854 used because it matches string pool constant short
Marking id:group_divider:2131230806 used because it matches string pool constant group
Marking attr:primaryActivityName:2130903240 used because it matches string pool constant primaryActivityName.className
Marking attr:secondaryActivityName:2130903254 used because it matches string pool constant secondaryActivityName
Marking attr:secondaryActivityName:2130903254 used because it matches string pool constant secondaryActivityName
Marking id:info:2131230813 used because it matches string pool constant info
Marking id:info:2131230813 used because it matches string pool constant info
Marking attr:primaryActivityName:2130903240 used because it matches string pool constant primaryActivity
Marking attr:secondaryActivityAction:2130903253 used because it matches string pool constant secondaryActivity
Marking attr:secondaryActivityName:2130903254 used because it matches string pool constant secondaryActivity
Marking attr:alpha:********** used because it matches string pool constant alpha
Marking attr:alpha:********** used because it matches string pool constant alpha
Marking attr:alphabeticModifiers:********** used because it matches string pool constant alpha
Marking attr:actionBarDivider:2130903040 used because it matches string pool constant action
Marking attr:actionBarItemBackground:2130903041 used because it matches string pool constant action
Marking attr:actionBarPopupTheme:2130903042 used because it matches string pool constant action
Marking attr:actionBarSize:2130903043 used because it matches string pool constant action
Marking attr:actionBarSplitStyle:2130903044 used because it matches string pool constant action
Marking attr:actionBarStyle:2130903045 used because it matches string pool constant action
Marking attr:actionBarTabBarStyle:2130903046 used because it matches string pool constant action
Marking attr:actionBarTabStyle:2130903047 used because it matches string pool constant action
Marking attr:actionBarTabTextStyle:2130903048 used because it matches string pool constant action
Marking attr:actionBarTheme:2130903049 used because it matches string pool constant action
Marking attr:actionBarWidgetTheme:2130903050 used because it matches string pool constant action
Marking attr:actionButtonStyle:2130903051 used because it matches string pool constant action
Marking attr:actionDropDownStyle:2130903052 used because it matches string pool constant action
Marking attr:actionLayout:2130903053 used because it matches string pool constant action
Marking attr:actionMenuTextAppearance:2130903054 used because it matches string pool constant action
Marking attr:actionMenuTextColor:2130903055 used because it matches string pool constant action
Marking attr:actionModeBackground:2130903056 used because it matches string pool constant action
Marking attr:actionModeCloseButtonStyle:2130903057 used because it matches string pool constant action
Marking attr:actionModeCloseContentDescription:2130903058 used because it matches string pool constant action
Marking attr:actionModeCloseDrawable:2130903059 used because it matches string pool constant action
Marking attr:actionModeCopyDrawable:********** used because it matches string pool constant action
Marking attr:actionModeCutDrawable:********** used because it matches string pool constant action
Marking attr:actionModeFindDrawable:********** used because it matches string pool constant action
Marking attr:actionModePasteDrawable:********** used because it matches string pool constant action
Marking attr:actionModePopupWindowStyle:********** used because it matches string pool constant action
Marking attr:actionModeSelectAllDrawable:********** used because it matches string pool constant action
Marking attr:actionModeShareDrawable:********** used because it matches string pool constant action
Marking attr:actionModeSplitBackground:********** used because it matches string pool constant action
Marking attr:actionModeStyle:********** used because it matches string pool constant action
Marking attr:actionModeTheme:********** used because it matches string pool constant action
Marking attr:actionModeWebSearchDrawable:********** used because it matches string pool constant action
Marking attr:actionOverflowButtonStyle:********** used because it matches string pool constant action
Marking attr:actionOverflowMenuStyle:********** used because it matches string pool constant action
Marking attr:actionProviderClass:********** used because it matches string pool constant action
Marking attr:actionViewClass:********** used because it matches string pool constant action
Marking id:action_bar:********** used because it matches string pool constant action
Marking id:action_bar_activity_content:********** used because it matches string pool constant action
Marking id:action_bar_container:********** used because it matches string pool constant action
Marking id:action_bar_root:********** used because it matches string pool constant action
Marking id:action_bar_spinner:********** used because it matches string pool constant action
Marking id:action_bar_subtitle:********** used because it matches string pool constant action
Marking id:action_bar_title:********** used because it matches string pool constant action
Marking id:action_container:********** used because it matches string pool constant action
Marking id:action_context_bar:********** used because it matches string pool constant action
Marking id:action_divider:2131230768 used because it matches string pool constant action
Marking id:action_image:2131230769 used because it matches string pool constant action
Marking id:action_menu_divider:2131230770 used because it matches string pool constant action
Marking id:action_menu_presenter:2131230771 used because it matches string pool constant action
Marking id:action_mode_bar:2131230772 used because it matches string pool constant action
Marking id:action_mode_bar_stub:2131230773 used because it matches string pool constant action
Marking id:action_mode_close_button:2131230774 used because it matches string pool constant action
Marking id:action_text:2131230775 used because it matches string pool constant action
Marking id:actions:2131230776 used because it matches string pool constant action
Marking id:text:2131230880 used because it matches string pool constant text
Marking attr:textAllCaps:2130903285 used because it matches string pool constant text
Marking attr:textAppearanceLargePopupMenu:2130903286 used because it matches string pool constant text
Marking attr:textAppearanceListItem:2130903287 used because it matches string pool constant text
Marking attr:textAppearanceListItemSecondary:2130903288 used because it matches string pool constant text
Marking attr:textAppearanceListItemSmall:2130903289 used because it matches string pool constant text
Marking attr:textAppearancePopupMenuHeader:2130903290 used because it matches string pool constant text
Marking attr:textAppearanceSearchResultSubtitle:2130903291 used because it matches string pool constant text
Marking attr:textAppearanceSearchResultTitle:2130903292 used because it matches string pool constant text
Marking attr:textAppearanceSmallPopupMenu:2130903293 used because it matches string pool constant text
Marking attr:textColorAlertDialogListItem:2130903294 used because it matches string pool constant text
Marking attr:textColorSearchUrl:2130903295 used because it matches string pool constant text
Marking attr:textLocale:2130903296 used because it matches string pool constant text
Marking id:text:2131230880 used because it matches string pool constant text
Marking id:text2:2131230881 used because it matches string pool constant text
Marking id:textSpacerNoButtons:2131230882 used because it matches string pool constant text
Marking id:textSpacerNoTitle:2131230883 used because it matches string pool constant text
Marking drawable:notify_panel_notification_icon_bg:2131165283 used because it matches string pool constant notify
Marking attr:menu:********** used because it matches string pool constant menu
Marking attr:menu:********** used because it matches string pool constant menu
Marking attr:background:********** used because it matches string pool constant background
Marking attr:background:********** used because it matches string pool constant background
Marking attr:backgroundSplit:********** used because it matches string pool constant background
Marking attr:backgroundStacked:********** used because it matches string pool constant background
Marking attr:backgroundTint:********** used because it matches string pool constant background
Marking attr:backgroundTintMode:********** used because it matches string pool constant background
Marking color:background_floating_material_dark:2131034141 used because it matches string pool constant background
Marking color:background_floating_material_light:2131034142 used because it matches string pool constant background
Marking color:background_material_dark:2131034143 used because it matches string pool constant background
Marking color:background_material_light:2131034144 used because it matches string pool constant background
Marking attr:height:********** used because it matches string pool constant height
Marking attr:height:********** used because it matches string pool constant height
Marking attr:secondaryActivityName:2130903254 used because it matches string pool constant secondaryActivityName.packageName
Marking attr:progressBarPadding:2130903241 used because it matches string pool constant progress
Marking attr:progressBarStyle:2130903242 used because it matches string pool constant progress
Marking id:progress_circular:2131230833 used because it matches string pool constant progress
Marking id:progress_horizontal:2131230834 used because it matches string pool constant progress
Marking color:error_color_material_dark:2131034157 used because it matches string pool constant error
Marking color:error_color_material_light:2131034158 used because it matches string pool constant error
Marking color:accent_material_dark:2131034137 used because it matches string pool constant acc
Marking color:accent_material_light:2131034138 used because it matches string pool constant acc
Marking id:accessibility_action_clickable_span:2131230726 used because it matches string pool constant acc
Marking id:accessibility_custom_action_0:2131230727 used because it matches string pool constant acc
Marking id:accessibility_custom_action_1:2131230728 used because it matches string pool constant acc
Marking id:accessibility_custom_action_10:2131230729 used because it matches string pool constant acc
Marking id:accessibility_custom_action_11:2131230730 used because it matches string pool constant acc
Marking id:accessibility_custom_action_12:2131230731 used because it matches string pool constant acc
Marking id:accessibility_custom_action_13:2131230732 used because it matches string pool constant acc
Marking id:accessibility_custom_action_14:2131230733 used because it matches string pool constant acc
Marking id:accessibility_custom_action_15:2131230734 used because it matches string pool constant acc
Marking id:accessibility_custom_action_16:2131230735 used because it matches string pool constant acc
Marking id:accessibility_custom_action_17:2131230736 used because it matches string pool constant acc
Marking id:accessibility_custom_action_18:2131230737 used because it matches string pool constant acc
Marking id:accessibility_custom_action_19:2131230738 used because it matches string pool constant acc
Marking id:accessibility_custom_action_2:2131230739 used because it matches string pool constant acc
Marking id:accessibility_custom_action_20:2131230740 used because it matches string pool constant acc
Marking id:accessibility_custom_action_21:2131230741 used because it matches string pool constant acc
Marking id:accessibility_custom_action_22:2131230742 used because it matches string pool constant acc
Marking id:accessibility_custom_action_23:2131230743 used because it matches string pool constant acc
Marking id:accessibility_custom_action_24:2131230744 used because it matches string pool constant acc
Marking id:accessibility_custom_action_25:2131230745 used because it matches string pool constant acc
Marking id:accessibility_custom_action_26:2131230746 used because it matches string pool constant acc
Marking id:accessibility_custom_action_27:2131230747 used because it matches string pool constant acc
Marking id:accessibility_custom_action_28:2131230748 used because it matches string pool constant acc
Marking id:accessibility_custom_action_29:2131230749 used because it matches string pool constant acc
Marking id:accessibility_custom_action_3:2131230750 used because it matches string pool constant acc
Marking id:accessibility_custom_action_30:2131230751 used because it matches string pool constant acc
Marking id:accessibility_custom_action_31:2131230752 used because it matches string pool constant acc
Marking id:accessibility_custom_action_4:2131230753 used because it matches string pool constant acc
Marking id:accessibility_custom_action_5:2131230754 used because it matches string pool constant acc
Marking id:accessibility_custom_action_6:2131230755 used because it matches string pool constant acc
Marking id:accessibility_custom_action_7:2131230756 used because it matches string pool constant acc
Marking id:accessibility_custom_action_8:2131230757 used because it matches string pool constant acc
Marking id:accessibility_custom_action_9:2131230758 used because it matches string pool constant acc
Marking id:message:2131230821 used because it matches string pool constant message
Marking id:message:2131230821 used because it matches string pool constant message
Marking attr:secondaryActivityName:2130903254 used because it matches string pool constant secondaryActivityName.className
Marking id:info:2131230813 used because it matches string pool constant info.displayFeatures
Marking attr:viewInflaterClass:2130903326 used because it matches string pool constant view
Marking id:view_tree_lifecycle_owner:2131230894 used because it matches string pool constant view
Marking id:view_tree_saved_state_registry_owner:2131230895 used because it matches string pool constant view
Marking id:view_tree_view_model_store_owner:2131230896 used because it matches string pool constant view
Marking attr:primaryActivityName:2130903240 used because it matches string pool constant primaryActivityName
Marking attr:primaryActivityName:2130903240 used because it matches string pool constant primaryActivityName
Marking color:androidx_core_ripple_material_light:2131034139 used because it matches string pool constant android
Marking color:androidx_core_secondary_text_default_material_light:2131034140 used because it matches string pool constant android
Marking id:androidx_window_activity_scope:2131230781 used because it matches string pool constant android
Marking attr:windowActionBar:2130903328 used because it matches string pool constant window
Marking attr:windowActionBarOverlay:2130903329 used because it matches string pool constant window
Marking attr:windowActionModeOverlay:2130903330 used because it matches string pool constant window
Marking attr:windowFixedHeightMajor:2130903331 used because it matches string pool constant window
Marking attr:windowFixedHeightMinor:2130903332 used because it matches string pool constant window
Marking attr:windowFixedWidthMajor:2130903333 used because it matches string pool constant window
Marking attr:windowFixedWidthMinor:2130903334 used because it matches string pool constant window
Marking attr:windowMinWidthMajor:2130903335 used because it matches string pool constant window
Marking attr:windowMinWidthMinor:2130903336 used because it matches string pool constant window
Marking attr:windowNoTitle:2130903337 used because it matches string pool constant window
Marking attr:itemPadding:********** used because it matches string pool constant item
Marking attr:primaryActivityName:2130903240 used because it matches string pool constant primaryActivityName.packageName
Marking attr:displayOptions:********** used because it matches string pool constant display
Marking color:notification_action_color_filter:2131034175 used because it matches string pool constant notification
Marking color:notification_icon_bg_color:2131034176 used because it matches string pool constant notification
Marking dimen:notification_action_icon_size:2131099745 used because it matches string pool constant notification
Marking dimen:notification_action_text_size:2131099746 used because it matches string pool constant notification
Marking dimen:notification_big_circle_margin:2131099747 used because it matches string pool constant notification
Marking dimen:notification_content_margin_start:2131099748 used because it matches string pool constant notification
Marking dimen:notification_large_icon_height:2131099749 used because it matches string pool constant notification
Marking dimen:notification_large_icon_width:2131099750 used because it matches string pool constant notification
Marking dimen:notification_main_column_padding_top:2131099751 used because it matches string pool constant notification
Marking dimen:notification_media_narrow_margin:2131099752 used because it matches string pool constant notification
Marking dimen:notification_right_icon_size:2131099753 used because it matches string pool constant notification
Marking dimen:notification_right_side_padding_top:2131099754 used because it matches string pool constant notification
Marking dimen:notification_small_icon_background_padding:2131099755 used because it matches string pool constant notification
Marking dimen:notification_small_icon_size_as_large:2131099756 used because it matches string pool constant notification
Marking dimen:notification_subtext_size:2131099757 used because it matches string pool constant notification
Marking dimen:notification_top_pad:2131099758 used because it matches string pool constant notification
Marking dimen:notification_top_pad_large_text:2131099759 used because it matches string pool constant notification
Marking drawable:notification_action_background:2131165272 used because it matches string pool constant notification
Marking drawable:notification_bg:2131165273 used because it matches string pool constant notification
Marking drawable:notification_bg_low:2131165274 used because it matches string pool constant notification
Marking drawable:notification_bg_low_normal:2131165275 used because it matches string pool constant notification
Marking drawable:notification_bg_low_pressed:2131165276 used because it matches string pool constant notification
Marking drawable:notification_bg_normal:2131165277 used because it matches string pool constant notification
Marking drawable:notification_bg_normal_pressed:2131165278 used because it matches string pool constant notification
Marking drawable:notification_icon_background:2131165279 used because it matches string pool constant notification
Marking drawable:notification_template_icon_bg:2131165280 used because it matches string pool constant notification
Marking drawable:notification_template_icon_low_bg:2131165281 used because it matches string pool constant notification
Marking drawable:notification_tile_bg:2131165282 used because it matches string pool constant notification
Marking id:notification_background:2131230827 used because it matches string pool constant notification
Marking id:notification_main_column:2131230828 used because it matches string pool constant notification
Marking id:notification_main_column_container:2131230829 used because it matches string pool constant notification
Marking layout:notification_action:2131427357 used because it matches string pool constant notification
Marking layout:notification_action_tombstone:2131427358 used because it matches string pool constant notification
Marking layout:notification_template_custom_big:2131427359 used because it matches string pool constant notification
Marking layout:notification_template_icon_group:2131427360 used because it matches string pool constant notification
Marking layout:notification_template_part_chronometer:2131427361 used because it matches string pool constant notification
Marking layout:notification_template_part_time:2131427362 used because it matches string pool constant notification
Marking attr:tooltipForegroundColor:2130903319 used because it matches string pool constant tooltip
Marking attr:tooltipFrameBackground:2130903320 used because it matches string pool constant tooltip
Marking attr:tooltipText:2130903321 used because it matches string pool constant tooltip
Marking color:tooltip_background_dark:2131034197 used because it matches string pool constant tooltip
Marking color:tooltip_background_light:2131034198 used because it matches string pool constant tooltip
Marking dimen:tooltip_corner_radius:2131099760 used because it matches string pool constant tooltip
Marking dimen:tooltip_horizontal_padding:2131099761 used because it matches string pool constant tooltip
Marking dimen:tooltip_margin:2131099762 used because it matches string pool constant tooltip
Marking dimen:tooltip_precise_anchor_extra_offset:2131099763 used because it matches string pool constant tooltip
Marking dimen:tooltip_precise_anchor_threshold:2131099764 used because it matches string pool constant tooltip
Marking dimen:tooltip_vertical_padding:2131099765 used because it matches string pool constant tooltip
Marking dimen:tooltip_y_offset_non_touch:2131099766 used because it matches string pool constant tooltip
Marking dimen:tooltip_y_offset_touch:2131099767 used because it matches string pool constant tooltip
Marking drawable:tooltip_frame_dark:********** used because it matches string pool constant tooltip
Marking drawable:tooltip_frame_light:********** used because it matches string pool constant tooltip
Marking attr:font:********** used because it matches string pool constant font
Marking attr:font:********** used because it matches string pool constant font
Marking attr:fontFamily:********** used because it matches string pool constant font
Marking attr:fontProviderAuthority:********** used because it matches string pool constant font
Marking attr:fontProviderCerts:********** used because it matches string pool constant font
Marking attr:fontProviderFetchStrategy:********** used because it matches string pool constant font
Marking attr:fontProviderFetchTimeout:********** used because it matches string pool constant font
Marking attr:fontProviderPackage:********** used because it matches string pool constant font
Marking attr:fontProviderQuery:********** used because it matches string pool constant font
Marking attr:fontProviderSystemFontFamily:********** used because it matches string pool constant font
Marking attr:fontStyle:********** used because it matches string pool constant font
Marking attr:fontVariationSettings:********** used because it matches string pool constant font
Marking attr:fontWeight:********** used because it matches string pool constant font
Marking id:image:********** used because it matches string pool constant image
Marking attr:imageButtonStyle:********** used because it matches string pool constant image
Marking id:image:********** used because it matches string pool constant image
Marking id:content:********** used because it matches string pool constant content
Marking attr:contentDescription:********** used because it matches string pool constant content
Marking attr:contentInsetEnd:********** used because it matches string pool constant content
Marking attr:contentInsetEndWithActions:********** used because it matches string pool constant content
Marking attr:contentInsetLeft:********** used because it matches string pool constant content
Marking attr:contentInsetRight:********** used because it matches string pool constant content
Marking attr:contentInsetStart:2130903138 used because it matches string pool constant content
Marking attr:contentInsetStartWithNavigation:2130903139 used because it matches string pool constant content
Marking id:content:********** used because it matches string pool constant content
Marking id:contentPanel:2131230793 used because it matches string pool constant content
Marking attr:itemPadding:********** used because it matches string pool constant it
Marking id:italic:2131230814 used because it matches string pool constant it
Marking id:off:2131230830 used because it matches string pool constant off
Marking id:off:2131230830 used because it matches string pool constant off
Marking attr:tintMode:2130903306 used because it matches string pool constant tintMode
Marking attr:tintMode:2130903306 used because it matches string pool constant tintMode
Marking id:on:2131230831 used because it matches string pool constant on
Marking id:on:2131230831 used because it matches string pool constant on
Marking attr:drawableBottomCompat:********** used because it matches string pool constant drawable
Marking attr:drawableEndCompat:********** used because it matches string pool constant drawable
Marking attr:drawableLeftCompat:********** used because it matches string pool constant drawable
Marking attr:drawableRightCompat:********** used because it matches string pool constant drawable
Marking attr:drawableSize:********** used because it matches string pool constant drawable
Marking attr:drawableStartCompat:********** used because it matches string pool constant drawable
Marking attr:drawableTint:********** used because it matches string pool constant drawable
Marking attr:drawableTintMode:********** used because it matches string pool constant drawable
Marking attr:drawableTopCompat:********** used because it matches string pool constant drawable
Marking attr:activityAction:********** used because it matches string pool constant activity
Marking attr:activityChooserViewStyle:********** used because it matches string pool constant activity
Marking attr:activityName:********** used because it matches string pool constant activity
Marking id:activity_chooser_view_content:2131230777 used because it matches string pool constant activity
Marking attr:queryBackground:2130903243 used because it matches string pool constant query
Marking attr:queryHint:2130903244 used because it matches string pool constant query
Marking attr:queryPatterns:2130903245 used because it matches string pool constant query
Marking id:none:2131230825 used because it matches string pool constant none
Marking id:none:2131230825 used because it matches string pool constant none
Marking attr:lineHeight:********** used because it matches string pool constant line
Marking id:line1:2131230815 used because it matches string pool constant line
Marking id:line3:2131230816 used because it matches string pool constant line
Marking anim:fragment_fast_out_extra_slow_in:2130771992 used because it matches string pool constant fragment
Marking animator:fragment_close_enter:2130837504 used because it matches string pool constant fragment
Marking animator:fragment_close_exit:2130837505 used because it matches string pool constant fragment
Marking animator:fragment_fade_enter:2130837506 used because it matches string pool constant fragment
Marking animator:fragment_fade_exit:2130837507 used because it matches string pool constant fragment
Marking animator:fragment_open_enter:2130837508 used because it matches string pool constant fragment
Marking animator:fragment_open_exit:2130837509 used because it matches string pool constant fragment
Marking id:fragment_container_view_tag:2131230805 used because it matches string pool constant fragment
@anim/abc_fade_in : reachable=false
@anim/abc_fade_out : reachable=false
@anim/abc_grow_fade_in_from_bottom : reachable=false
    @integer/abc_config_activityDefaultDur
    @integer/abc_config_activityShortDur
@anim/abc_popup_enter : reachable=false
    @integer/abc_config_activityShortDur
@anim/abc_popup_exit : reachable=false
    @integer/abc_config_activityShortDur
@anim/abc_shrink_fade_out_from_bottom : reachable=false
    @integer/abc_config_activityDefaultDur
    @integer/abc_config_activityShortDur
@anim/abc_slide_in_bottom : reachable=false
@anim/abc_slide_in_top : reachable=false
@anim/abc_slide_out_bottom : reachable=false
@anim/abc_slide_out_top : reachable=false
@anim/abc_tooltip_enter : reachable=false
    @integer/config_tooltipAnimTime
@anim/abc_tooltip_exit : reachable=false
    @integer/config_tooltipAnimTime
@anim/btn_checkbox_to_checked_box_inner_merged_animation : reachable=false
    @interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1
    @interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0
@anim/btn_checkbox_to_checked_box_outer_merged_animation : reachable=false
    @interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1
    @interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0
@anim/btn_checkbox_to_checked_icon_null_animation : reachable=false
    @interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1
@anim/btn_checkbox_to_unchecked_box_inner_merged_animation : reachable=false
    @interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1
    @interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0
@anim/btn_checkbox_to_unchecked_check_path_merged_animation : reachable=false
    @interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1
    @interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0
@anim/btn_checkbox_to_unchecked_icon_null_animation : reachable=false
    @interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1
@anim/btn_radio_to_off_mtrl_dot_group_animation : reachable=false
    @interpolator/fast_out_slow_in
@anim/btn_radio_to_off_mtrl_ring_outer_animation : reachable=false
    @interpolator/fast_out_slow_in
    @interpolator/btn_radio_to_off_mtrl_animation_interpolator_0
@anim/btn_radio_to_off_mtrl_ring_outer_path_animation : reachable=false
    @interpolator/fast_out_slow_in
@anim/btn_radio_to_on_mtrl_dot_group_animation : reachable=false
    @interpolator/fast_out_slow_in
@anim/btn_radio_to_on_mtrl_ring_outer_animation : reachable=false
    @interpolator/fast_out_slow_in
@anim/btn_radio_to_on_mtrl_ring_outer_path_animation : reachable=false
    @interpolator/btn_radio_to_on_mtrl_animation_interpolator_0
    @interpolator/fast_out_slow_in
@anim/fragment_fast_out_extra_slow_in : reachable=true
@animator/fragment_close_enter : reachable=true
    @anim/fragment_fast_out_extra_slow_in
@animator/fragment_close_exit : reachable=true
    @anim/fragment_fast_out_extra_slow_in
@animator/fragment_fade_enter : reachable=true
@animator/fragment_fade_exit : reachable=true
@animator/fragment_open_enter : reachable=true
    @anim/fragment_fast_out_extra_slow_in
@animator/fragment_open_exit : reachable=true
    @anim/fragment_fast_out_extra_slow_in
@attr/actionBarDivider : reachable=true
@attr/actionBarItemBackground : reachable=true
@attr/actionBarPopupTheme : reachable=true
@attr/actionBarSize : reachable=true
@attr/actionBarSplitStyle : reachable=true
@attr/actionBarStyle : reachable=true
@attr/actionBarTabBarStyle : reachable=true
@attr/actionBarTabStyle : reachable=true
@attr/actionBarTabTextStyle : reachable=true
@attr/actionBarTheme : reachable=true
@attr/actionBarWidgetTheme : reachable=true
@attr/actionButtonStyle : reachable=true
@attr/actionDropDownStyle : reachable=true
@attr/actionLayout : reachable=true
@attr/actionMenuTextAppearance : reachable=true
@attr/actionMenuTextColor : reachable=true
@attr/actionModeBackground : reachable=true
@attr/actionModeCloseButtonStyle : reachable=true
@attr/actionModeCloseContentDescription : reachable=true
@attr/actionModeCloseDrawable : reachable=true
@attr/actionModeCopyDrawable : reachable=true
@attr/actionModeCutDrawable : reachable=true
@attr/actionModeFindDrawable : reachable=true
@attr/actionModePasteDrawable : reachable=true
@attr/actionModePopupWindowStyle : reachable=true
@attr/actionModeSelectAllDrawable : reachable=true
@attr/actionModeShareDrawable : reachable=true
@attr/actionModeSplitBackground : reachable=true
@attr/actionModeStyle : reachable=true
@attr/actionModeTheme : reachable=true
@attr/actionModeWebSearchDrawable : reachable=true
@attr/actionOverflowButtonStyle : reachable=true
@attr/actionOverflowMenuStyle : reachable=true
@attr/actionProviderClass : reachable=true
@attr/actionViewClass : reachable=true
@attr/activityAction : reachable=true
@attr/activityChooserViewStyle : reachable=true
@attr/activityName : reachable=true
@attr/alertDialogButtonGroupStyle : reachable=false
@attr/alertDialogCenterButtons : reachable=true
@attr/alertDialogStyle : reachable=true
@attr/alertDialogTheme : reachable=true
@attr/allowStacking : reachable=false
@attr/alpha : reachable=true
@attr/alphabeticModifiers : reachable=true
@attr/alwaysExpand : reachable=false
@attr/arrowHeadLength : reachable=false
@attr/arrowShaftLength : reachable=false
@attr/autoCompleteTextViewStyle : reachable=true
@attr/autoSizeMaxTextSize : reachable=false
@attr/autoSizeMinTextSize : reachable=false
@attr/autoSizePresetSizes : reachable=false
@attr/autoSizeStepGranularity : reachable=false
@attr/autoSizeTextType : reachable=false
@attr/background : reachable=true
@attr/backgroundSplit : reachable=true
@attr/backgroundStacked : reachable=true
@attr/backgroundTint : reachable=true
@attr/backgroundTintMode : reachable=true
@attr/barLength : reachable=false
@attr/borderlessButtonStyle : reachable=false
@attr/buttonBarButtonStyle : reachable=false
@attr/buttonBarNegativeButtonStyle : reachable=false
@attr/buttonBarNeutralButtonStyle : reachable=false
@attr/buttonBarPositiveButtonStyle : reachable=false
@attr/buttonBarStyle : reachable=false
@attr/buttonCompat : reachable=false
@attr/buttonGravity : reachable=false
@attr/buttonIconDimen : reachable=false
@attr/buttonPanelSideLayout : reachable=false
@attr/buttonStyle : reachable=true
@attr/buttonStyleSmall : reachable=false
@attr/buttonTint : reachable=false
@attr/buttonTintMode : reachable=false
@attr/checkboxStyle : reachable=true
@attr/checkedTextViewStyle : reachable=false
@attr/clearTop : reachable=false
@attr/closeIcon : reachable=false
@attr/closeItemLayout : reachable=false
@attr/collapseContentDescription : reachable=false
@attr/collapseIcon : reachable=false
@attr/color : reachable=false
@attr/colorAccent : reachable=true
@attr/colorBackgroundFloating : reachable=false
@attr/colorButtonNormal : reachable=true
@attr/colorControlActivated : reachable=true
@attr/colorControlHighlight : reachable=true
@attr/colorControlNormal : reachable=true
@attr/colorError : reachable=false
@attr/colorPrimary : reachable=false
@attr/colorPrimaryDark : reachable=false
@attr/colorSwitchThumbNormal : reachable=true
@attr/commitIcon : reachable=false
@attr/contentDescription : reachable=true
@attr/contentInsetEnd : reachable=true
@attr/contentInsetEndWithActions : reachable=true
@attr/contentInsetLeft : reachable=true
@attr/contentInsetRight : reachable=true
@attr/contentInsetStart : reachable=true
@attr/contentInsetStartWithNavigation : reachable=true
@attr/controlBackground : reachable=false
@attr/customNavigationLayout : reachable=false
@attr/defaultQueryHint : reachable=false
@attr/dialogCornerRadius : reachable=false
@attr/dialogPreferredPadding : reachable=false
@attr/dialogTheme : reachable=true
@attr/displayOptions : reachable=true
@attr/divider : reachable=false
@attr/dividerHorizontal : reachable=false
@attr/dividerPadding : reachable=false
@attr/dividerVertical : reachable=false
@attr/drawableBottomCompat : reachable=true
@attr/drawableEndCompat : reachable=true
@attr/drawableLeftCompat : reachable=true
@attr/drawableRightCompat : reachable=true
@attr/drawableSize : reachable=true
@attr/drawableStartCompat : reachable=true
@attr/drawableTint : reachable=true
@attr/drawableTintMode : reachable=true
@attr/drawableTopCompat : reachable=true
@attr/drawerArrowStyle : reachable=false
@attr/dropDownListViewStyle : reachable=true
@attr/dropdownListPreferredItemHeight : reachable=false
@attr/editTextBackground : reachable=false
@attr/editTextColor : reachable=false
@attr/editTextStyle : reachable=true
@attr/elevation : reachable=false
@attr/expandActivityOverflowButtonDrawable : reachable=false
@attr/finishPrimaryWithSecondary : reachable=false
@attr/finishSecondaryWithPrimary : reachable=false
@attr/firstBaselineToTopHeight : reachable=false
@attr/font : reachable=true
@attr/fontFamily : reachable=true
@attr/fontProviderAuthority : reachable=true
@attr/fontProviderCerts : reachable=true
@attr/fontProviderFetchStrategy : reachable=true
@attr/fontProviderFetchTimeout : reachable=true
@attr/fontProviderPackage : reachable=true
@attr/fontProviderQuery : reachable=true
@attr/fontProviderSystemFontFamily : reachable=true
@attr/fontStyle : reachable=true
@attr/fontVariationSettings : reachable=true
@attr/fontWeight : reachable=true
@attr/gapBetweenBars : reachable=false
@attr/goIcon : reachable=false
@attr/height : reachable=true
@attr/hideOnContentScroll : reachable=false
@attr/homeAsUpIndicator : reachable=false
@attr/homeLayout : reachable=false
@attr/icon : reachable=false
@attr/iconTint : reachable=false
@attr/iconTintMode : reachable=false
@attr/iconifiedByDefault : reachable=false
@attr/imageButtonStyle : reachable=true
@attr/indeterminateProgressStyle : reachable=false
@attr/initialActivityCount : reachable=false
@attr/isLightTheme : reachable=false
@attr/itemPadding : reachable=true
@attr/lastBaselineToBottomHeight : reachable=false
@attr/layout : reachable=false
@attr/lineHeight : reachable=true
@attr/listChoiceBackgroundIndicator : reachable=true
@attr/listChoiceIndicatorMultipleAnimated : reachable=true
@attr/listChoiceIndicatorSingleAnimated : reachable=true
@attr/listDividerAlertDialog : reachable=true
@attr/listItemLayout : reachable=true
@attr/listLayout : reachable=true
@attr/listMenuViewStyle : reachable=true
@attr/listPopupWindowStyle : reachable=true
@attr/listPreferredItemHeight : reachable=true
@attr/listPreferredItemHeightLarge : reachable=true
@attr/listPreferredItemHeightSmall : reachable=true
@attr/listPreferredItemPaddingEnd : reachable=true
@attr/listPreferredItemPaddingLeft : reachable=true
@attr/listPreferredItemPaddingRight : reachable=true
@attr/listPreferredItemPaddingStart : reachable=true
@attr/logo : reachable=false
@attr/logoDescription : reachable=false
@attr/maxButtonHeight : reachable=false
@attr/measureWithLargestChild : reachable=false
@attr/menu : reachable=true
@attr/multiChoiceItemLayout : reachable=false
@attr/navigationContentDescription : reachable=false
@attr/navigationIcon : reachable=false
@attr/navigationMode : reachable=false
@attr/nestedScrollViewStyle : reachable=true
@attr/numericModifiers : reachable=false
@attr/overlapAnchor : reachable=false
@attr/paddingBottomNoButtons : reachable=false
@attr/paddingEnd : reachable=false
@attr/paddingStart : reachable=false
@attr/paddingTopNoTitle : reachable=false
@attr/panelBackground : reachable=false
@attr/panelMenuListTheme : reachable=true
@attr/panelMenuListWidth : reachable=false
@attr/placeholderActivityName : reachable=false
@attr/popupMenuStyle : reachable=false
@attr/popupTheme : reachable=false
@attr/popupWindowStyle : reachable=false
@attr/preserveIconSpacing : reachable=false
@attr/primaryActivityName : reachable=true
@attr/progressBarPadding : reachable=true
@attr/progressBarStyle : reachable=true
@attr/queryBackground : reachable=true
@attr/queryHint : reachable=true
@attr/queryPatterns : reachable=true
@attr/radioButtonStyle : reachable=true
@attr/ratingBarStyle : reachable=true
@attr/ratingBarStyleIndicator : reachable=false
@attr/ratingBarStyleSmall : reachable=false
@attr/searchHintIcon : reachable=false
@attr/searchIcon : reachable=false
@attr/searchViewStyle : reachable=true
@attr/secondaryActivityAction : reachable=true
@attr/secondaryActivityName : reachable=true
@attr/seekBarStyle : reachable=true
@attr/selectableItemBackground : reachable=false
@attr/selectableItemBackgroundBorderless : reachable=false
@attr/shortcutMatchRequired : reachable=true
@attr/showAsAction : reachable=false
@attr/showDividers : reachable=false
@attr/showText : reachable=false
@attr/showTitle : reachable=false
@attr/singleChoiceItemLayout : reachable=false
@attr/spinBars : reachable=false
@attr/spinnerDropDownItemStyle : reachable=false
@attr/spinnerStyle : reachable=true
@attr/splitLayoutDirection : reachable=false
@attr/splitMinSmallestWidth : reachable=false
@attr/splitMinWidth : reachable=false
@attr/splitRatio : reachable=false
@attr/splitTrack : reachable=false
@attr/srcCompat : reachable=false
@attr/state_above_anchor : reachable=true
@attr/subMenuArrow : reachable=false
@attr/submitBackground : reachable=false
@attr/subtitle : reachable=false
@attr/subtitleTextAppearance : reachable=false
@attr/subtitleTextColor : reachable=false
@attr/subtitleTextStyle : reachable=false
@attr/suggestionRowLayout : reachable=false
@attr/switchMinWidth : reachable=false
@attr/switchPadding : reachable=false
@attr/switchStyle : reachable=false
@attr/switchTextAppearance : reachable=false
@attr/textAllCaps : reachable=true
@attr/textAppearanceLargePopupMenu : reachable=true
@attr/textAppearanceListItem : reachable=true
@attr/textAppearanceListItemSecondary : reachable=true
@attr/textAppearanceListItemSmall : reachable=true
@attr/textAppearancePopupMenuHeader : reachable=true
@attr/textAppearanceSearchResultSubtitle : reachable=true
@attr/textAppearanceSearchResultTitle : reachable=true
@attr/textAppearanceSmallPopupMenu : reachable=true
@attr/textColorAlertDialogListItem : reachable=true
@attr/textColorSearchUrl : reachable=true
@attr/textLocale : reachable=true
@attr/theme : reachable=false
@attr/thickness : reachable=false
@attr/thumbTextPadding : reachable=false
@attr/thumbTint : reachable=false
@attr/thumbTintMode : reachable=false
@attr/tickMark : reachable=false
@attr/tickMarkTint : reachable=false
@attr/tickMarkTintMode : reachable=false
@attr/tint : reachable=true
@attr/tintMode : reachable=true
@attr/title : reachable=false
@attr/titleMargin : reachable=false
@attr/titleMarginBottom : reachable=false
@attr/titleMarginEnd : reachable=false
@attr/titleMarginStart : reachable=false
@attr/titleMarginTop : reachable=false
@attr/titleMargins : reachable=false
@attr/titleTextAppearance : reachable=false
@attr/titleTextColor : reachable=false
@attr/titleTextStyle : reachable=false
@attr/toolbarNavigationButtonStyle : reachable=true
@attr/toolbarStyle : reachable=true
@attr/tooltipForegroundColor : reachable=true
@attr/tooltipFrameBackground : reachable=true
@attr/tooltipText : reachable=true
@attr/track : reachable=false
@attr/trackTint : reachable=false
@attr/trackTintMode : reachable=false
@attr/ttcIndex : reachable=false
@attr/viewInflaterClass : reachable=true
@attr/voiceIcon : reachable=false
@attr/windowActionBar : reachable=true
@attr/windowActionBarOverlay : reachable=true
@attr/windowActionModeOverlay : reachable=true
@attr/windowFixedHeightMajor : reachable=true
@attr/windowFixedHeightMinor : reachable=true
@attr/windowFixedWidthMajor : reachable=true
@attr/windowFixedWidthMinor : reachable=true
@attr/windowMinWidthMajor : reachable=true
@attr/windowMinWidthMinor : reachable=true
@attr/windowNoTitle : reachable=true
@bool/abc_action_bar_embed_tabs : reachable=true
@bool/abc_config_actionMenuItemAllCaps : reachable=false
@color/abc_background_cache_hint_selector_material_dark : reachable=false
    @color/background_material_dark
@color/abc_background_cache_hint_selector_material_light : reachable=false
    @color/background_material_light
@color/abc_btn_colored_borderless_text_material : reachable=false
    @attr/colorAccent
@color/abc_btn_colored_text_material : reachable=false
@color/abc_color_highlight_material : reachable=false
    @dimen/highlight_alpha_material_colored
@color/abc_decor_view_status_guard : reachable=true
@color/abc_decor_view_status_guard_light : reachable=true
@color/abc_hint_foreground_material_dark : reachable=false
    @color/foreground_material_dark
    @dimen/hint_pressed_alpha_material_dark
    @dimen/hint_alpha_material_dark
@color/abc_hint_foreground_material_light : reachable=false
    @color/foreground_material_light
    @dimen/hint_pressed_alpha_material_light
    @dimen/hint_alpha_material_light
@color/abc_primary_text_disable_only_material_dark : reachable=false
    @color/bright_foreground_disabled_material_dark
    @color/bright_foreground_material_dark
@color/abc_primary_text_disable_only_material_light : reachable=false
    @color/bright_foreground_disabled_material_light
    @color/bright_foreground_material_light
@color/abc_primary_text_material_dark : reachable=false
    @color/primary_text_disabled_material_dark
    @color/primary_text_default_material_dark
@color/abc_primary_text_material_light : reachable=false
    @color/primary_text_disabled_material_light
    @color/primary_text_default_material_light
@color/abc_search_url_text : reachable=false
    @color/abc_search_url_text_pressed
    @color/abc_search_url_text_selected
    @color/abc_search_url_text_normal
@color/abc_search_url_text_normal : reachable=false
@color/abc_search_url_text_pressed : reachable=false
@color/abc_search_url_text_selected : reachable=false
@color/abc_secondary_text_material_dark : reachable=false
    @color/secondary_text_disabled_material_dark
    @color/secondary_text_default_material_dark
@color/abc_secondary_text_material_light : reachable=false
    @color/secondary_text_disabled_material_light
    @color/secondary_text_default_material_light
@color/abc_tint_btn_checkable : reachable=true
    @attr/colorControlNormal
    @attr/colorControlActivated
@color/abc_tint_default : reachable=true
    @attr/colorControlNormal
    @attr/colorControlActivated
@color/abc_tint_edittext : reachable=true
    @attr/colorControlNormal
    @attr/colorControlActivated
@color/abc_tint_seek_thumb : reachable=true
    @attr/colorControlActivated
@color/abc_tint_spinner : reachable=true
    @attr/colorControlNormal
    @attr/colorControlActivated
@color/abc_tint_switch_track : reachable=true
    @attr/colorControlActivated
@color/accent_material_dark : reachable=true
    @color/material_deep_teal_200
@color/accent_material_light : reachable=true
    @color/material_deep_teal_500
@color/androidx_core_ripple_material_light : reachable=true
@color/androidx_core_secondary_text_default_material_light : reachable=true
@color/background_floating_material_dark : reachable=true
    @color/material_grey_800
@color/background_floating_material_light : reachable=true
@color/background_material_dark : reachable=true
    @color/material_grey_850
@color/background_material_light : reachable=true
    @color/material_grey_50
@color/bright_foreground_disabled_material_dark : reachable=false
@color/bright_foreground_disabled_material_light : reachable=false
@color/bright_foreground_inverse_material_dark : reachable=false
    @color/bright_foreground_material_light
@color/bright_foreground_inverse_material_light : reachable=false
    @color/bright_foreground_material_dark
@color/bright_foreground_material_dark : reachable=false
@color/bright_foreground_material_light : reachable=false
@color/button_material_dark : reachable=false
@color/button_material_light : reachable=false
@color/dim_foreground_disabled_material_dark : reachable=false
@color/dim_foreground_disabled_material_light : reachable=false
@color/dim_foreground_material_dark : reachable=false
@color/dim_foreground_material_light : reachable=false
@color/error_color_material_dark : reachable=true
@color/error_color_material_light : reachable=true
@color/foreground_material_dark : reachable=false
@color/foreground_material_light : reachable=false
@color/highlighted_text_material_dark : reachable=false
@color/highlighted_text_material_light : reachable=false
@color/material_blue_grey_800 : reachable=false
@color/material_blue_grey_900 : reachable=false
@color/material_blue_grey_950 : reachable=false
@color/material_deep_teal_200 : reachable=false
@color/material_deep_teal_500 : reachable=false
@color/material_grey_100 : reachable=false
@color/material_grey_300 : reachable=false
@color/material_grey_50 : reachable=false
@color/material_grey_600 : reachable=false
@color/material_grey_800 : reachable=false
@color/material_grey_850 : reachable=false
@color/material_grey_900 : reachable=false
@color/notification_action_color_filter : reachable=true
    @color/androidx_core_secondary_text_default_material_light
@color/notification_icon_bg_color : reachable=true
@color/primary_dark_material_dark : reachable=false
@color/primary_dark_material_light : reachable=false
    @color/material_grey_600
@color/primary_material_dark : reachable=false
    @color/material_grey_900
@color/primary_material_light : reachable=false
    @color/material_grey_100
@color/primary_text_default_material_dark : reachable=false
@color/primary_text_default_material_light : reachable=false
@color/primary_text_disabled_material_dark : reachable=false
@color/primary_text_disabled_material_light : reachable=false
@color/ripple_material_dark : reachable=false
@color/ripple_material_light : reachable=false
@color/secondary_text_default_material_dark : reachable=false
@color/secondary_text_default_material_light : reachable=false
@color/secondary_text_disabled_material_dark : reachable=false
@color/secondary_text_disabled_material_light : reachable=false
@color/switch_thumb_disabled_material_dark : reachable=false
@color/switch_thumb_disabled_material_light : reachable=false
@color/switch_thumb_material_dark : reachable=false
    @color/switch_thumb_disabled_material_dark
    @color/switch_thumb_normal_material_dark
@color/switch_thumb_material_light : reachable=false
    @color/switch_thumb_disabled_material_light
    @color/switch_thumb_normal_material_light
@color/switch_thumb_normal_material_dark : reachable=false
@color/switch_thumb_normal_material_light : reachable=false
@color/tooltip_background_dark : reachable=true
@color/tooltip_background_light : reachable=true
@dimen/abc_action_bar_content_inset_material : reachable=false
@dimen/abc_action_bar_content_inset_with_nav : reachable=false
@dimen/abc_action_bar_default_height_material : reachable=false
@dimen/abc_action_bar_default_padding_end_material : reachable=false
@dimen/abc_action_bar_default_padding_start_material : reachable=false
@dimen/abc_action_bar_elevation_material : reachable=false
@dimen/abc_action_bar_icon_vertical_padding_material : reachable=false
@dimen/abc_action_bar_overflow_padding_end_material : reachable=false
@dimen/abc_action_bar_overflow_padding_start_material : reachable=false
@dimen/abc_action_bar_stacked_max_height : reachable=true
@dimen/abc_action_bar_stacked_tab_max_width : reachable=true
@dimen/abc_action_bar_subtitle_bottom_margin_material : reachable=false
@dimen/abc_action_bar_subtitle_top_margin_material : reachable=false
@dimen/abc_action_button_min_height_material : reachable=false
@dimen/abc_action_button_min_width_material : reachable=false
@dimen/abc_action_button_min_width_overflow_material : reachable=false
@dimen/abc_alert_dialog_button_bar_height : reachable=false
@dimen/abc_alert_dialog_button_dimen : reachable=false
@dimen/abc_button_inset_horizontal_material : reachable=false
    @dimen/abc_control_inset_material
@dimen/abc_button_inset_vertical_material : reachable=false
@dimen/abc_button_padding_horizontal_material : reachable=false
@dimen/abc_button_padding_vertical_material : reachable=false
    @dimen/abc_control_padding_material
@dimen/abc_cascading_menus_min_smallest_width : reachable=true
@dimen/abc_config_prefDialogWidth : reachable=true
@dimen/abc_control_corner_material : reachable=false
@dimen/abc_control_inset_material : reachable=false
@dimen/abc_control_padding_material : reachable=false
@dimen/abc_dialog_corner_radius_material : reachable=false
@dimen/abc_dialog_fixed_height_major : reachable=false
@dimen/abc_dialog_fixed_height_minor : reachable=false
@dimen/abc_dialog_fixed_width_major : reachable=false
@dimen/abc_dialog_fixed_width_minor : reachable=false
@dimen/abc_dialog_list_padding_bottom_no_buttons : reachable=false
@dimen/abc_dialog_list_padding_top_no_title : reachable=false
@dimen/abc_dialog_min_width_major : reachable=false
@dimen/abc_dialog_min_width_minor : reachable=false
@dimen/abc_dialog_padding_material : reachable=false
@dimen/abc_dialog_padding_top_material : reachable=false
@dimen/abc_dialog_title_divider_material : reachable=false
@dimen/abc_disabled_alpha_material_dark : reachable=false
@dimen/abc_disabled_alpha_material_light : reachable=false
@dimen/abc_dropdownitem_icon_width : reachable=true
@dimen/abc_dropdownitem_text_padding_left : reachable=true
@dimen/abc_dropdownitem_text_padding_right : reachable=false
@dimen/abc_edit_text_inset_bottom_material : reachable=false
@dimen/abc_edit_text_inset_horizontal_material : reachable=false
@dimen/abc_edit_text_inset_top_material : reachable=false
@dimen/abc_floating_window_z : reachable=false
@dimen/abc_list_item_height_large_material : reachable=false
@dimen/abc_list_item_height_material : reachable=false
@dimen/abc_list_item_height_small_material : reachable=false
@dimen/abc_list_item_padding_horizontal_material : reachable=false
    @dimen/abc_action_bar_content_inset_material
@dimen/abc_panel_menu_list_width : reachable=false
@dimen/abc_progress_bar_height_material : reachable=false
@dimen/abc_search_view_preferred_height : reachable=true
@dimen/abc_search_view_preferred_width : reachable=true
@dimen/abc_seekbar_track_background_height_material : reachable=false
@dimen/abc_seekbar_track_progress_height_material : reachable=false
@dimen/abc_select_dialog_padding_start_material : reachable=false
@dimen/abc_star_big : reachable=true
@dimen/abc_star_medium : reachable=true
@dimen/abc_star_small : reachable=true
@dimen/abc_switch_padding : reachable=false
@dimen/abc_text_size_body_1_material : reachable=false
@dimen/abc_text_size_body_2_material : reachable=false
@dimen/abc_text_size_button_material : reachable=false
@dimen/abc_text_size_caption_material : reachable=false
@dimen/abc_text_size_display_1_material : reachable=false
@dimen/abc_text_size_display_2_material : reachable=false
@dimen/abc_text_size_display_3_material : reachable=false
@dimen/abc_text_size_display_4_material : reachable=false
@dimen/abc_text_size_headline_material : reachable=false
@dimen/abc_text_size_large_material : reachable=false
@dimen/abc_text_size_medium_material : reachable=false
@dimen/abc_text_size_menu_header_material : reachable=false
@dimen/abc_text_size_menu_material : reachable=false
@dimen/abc_text_size_small_material : reachable=false
@dimen/abc_text_size_subhead_material : reachable=false
@dimen/abc_text_size_subtitle_material_toolbar : reachable=false
@dimen/abc_text_size_title_material : reachable=false
@dimen/abc_text_size_title_material_toolbar : reachable=false
@dimen/compat_button_inset_horizontal_material : reachable=false
@dimen/compat_button_inset_vertical_material : reachable=false
@dimen/compat_button_padding_horizontal_material : reachable=false
@dimen/compat_button_padding_vertical_material : reachable=false
@dimen/compat_control_corner_material : reachable=false
@dimen/compat_notification_large_icon_max_height : reachable=false
@dimen/compat_notification_large_icon_max_width : reachable=false
@dimen/disabled_alpha_material_dark : reachable=false
@dimen/disabled_alpha_material_light : reachable=false
@dimen/highlight_alpha_material_colored : reachable=false
@dimen/highlight_alpha_material_dark : reachable=false
@dimen/highlight_alpha_material_light : reachable=false
@dimen/hint_alpha_material_dark : reachable=false
@dimen/hint_alpha_material_light : reachable=false
@dimen/hint_pressed_alpha_material_dark : reachable=false
@dimen/hint_pressed_alpha_material_light : reachable=false
@dimen/notification_action_icon_size : reachable=true
@dimen/notification_action_text_size : reachable=true
@dimen/notification_big_circle_margin : reachable=true
@dimen/notification_content_margin_start : reachable=true
@dimen/notification_large_icon_height : reachable=true
@dimen/notification_large_icon_width : reachable=true
@dimen/notification_main_column_padding_top : reachable=true
@dimen/notification_media_narrow_margin : reachable=true
@dimen/notification_right_icon_size : reachable=true
@dimen/notification_right_side_padding_top : reachable=true
@dimen/notification_small_icon_background_padding : reachable=true
@dimen/notification_small_icon_size_as_large : reachable=true
@dimen/notification_subtext_size : reachable=true
@dimen/notification_top_pad : reachable=true
@dimen/notification_top_pad_large_text : reachable=true
@dimen/tooltip_corner_radius : reachable=true
@dimen/tooltip_horizontal_padding : reachable=true
@dimen/tooltip_margin : reachable=true
@dimen/tooltip_precise_anchor_extra_offset : reachable=true
@dimen/tooltip_precise_anchor_threshold : reachable=true
@dimen/tooltip_vertical_padding : reachable=true
@dimen/tooltip_y_offset_non_touch : reachable=true
@dimen/tooltip_y_offset_touch : reachable=true
@drawable/abc_ab_share_pack_mtrl_alpha : reachable=true
@drawable/abc_action_bar_item_background_material : reachable=false
@drawable/abc_btn_borderless_material : reachable=true
    @drawable/abc_btn_default_mtrl_shape
@drawable/abc_btn_check_material : reachable=true
    @drawable/abc_btn_check_to_on_mtrl_015
    @drawable/abc_btn_check_to_on_mtrl_000
@drawable/abc_btn_check_material_anim : reachable=true
    @drawable/btn_checkbox_checked_mtrl
    @drawable/btn_checkbox_unchecked_mtrl
    @drawable/btn_checkbox_unchecked_to_checked_mtrl_animation
    @drawable/btn_checkbox_checked_to_unchecked_mtrl_animation
@drawable/abc_btn_check_to_on_mtrl_000 : reachable=false
@drawable/abc_btn_check_to_on_mtrl_015 : reachable=false
@drawable/abc_btn_colored_material : reachable=true
    @dimen/abc_button_inset_horizontal_material
    @dimen/abc_button_inset_vertical_material
    @dimen/abc_control_corner_material
    @dimen/abc_button_padding_vertical_material
    @dimen/abc_button_padding_horizontal_material
@drawable/abc_btn_default_mtrl_shape : reachable=true
    @dimen/abc_button_inset_horizontal_material
    @dimen/abc_button_inset_vertical_material
    @dimen/abc_control_corner_material
    @dimen/abc_button_padding_vertical_material
    @dimen/abc_button_padding_horizontal_material
@drawable/abc_btn_radio_material : reachable=true
    @drawable/abc_btn_radio_to_on_mtrl_015
    @drawable/abc_btn_radio_to_on_mtrl_000
@drawable/abc_btn_radio_material_anim : reachable=true
    @drawable/btn_radio_on_mtrl
    @drawable/btn_radio_off_mtrl
    @drawable/btn_radio_on_to_off_mtrl_animation
    @drawable/btn_radio_off_to_on_mtrl_animation
@drawable/abc_btn_radio_to_on_mtrl_000 : reachable=false
@drawable/abc_btn_radio_to_on_mtrl_015 : reachable=false
@drawable/abc_btn_switch_to_on_mtrl_00001 : reachable=false
@drawable/abc_btn_switch_to_on_mtrl_00012 : reachable=false
@drawable/abc_cab_background_internal_bg : reachable=true
@drawable/abc_cab_background_top_material : reachable=true
@drawable/abc_cab_background_top_mtrl_alpha : reachable=true
@drawable/abc_control_background_material : reachable=false
    @color/abc_color_highlight_material
@drawable/abc_dialog_material_background : reachable=true
    @attr/dialogCornerRadius
@drawable/abc_edit_text_material : reachable=true
    @dimen/abc_edit_text_inset_horizontal_material
    @dimen/abc_edit_text_inset_top_material
    @dimen/abc_edit_text_inset_bottom_material
    @drawable/abc_textfield_default_mtrl_alpha
    @attr/colorControlNormal
    @drawable/abc_textfield_activated_mtrl_alpha
    @attr/colorControlActivated
@drawable/abc_ic_ab_back_material : reachable=true
    @attr/colorControlNormal
@drawable/abc_ic_arrow_drop_right_black_24dp : reachable=false
    @attr/colorControlNormal
@drawable/abc_ic_clear_material : reachable=false
    @attr/colorControlNormal
@drawable/abc_ic_commit_search_api_mtrl_alpha : reachable=true
@drawable/abc_ic_go_search_api_material : reachable=false
    @attr/colorControlNormal
@drawable/abc_ic_menu_copy_mtrl_am_alpha : reachable=true
@drawable/abc_ic_menu_cut_mtrl_alpha : reachable=true
@drawable/abc_ic_menu_overflow_material : reachable=false
    @attr/colorControlNormal
@drawable/abc_ic_menu_paste_mtrl_am_alpha : reachable=true
@drawable/abc_ic_menu_selectall_mtrl_alpha : reachable=true
@drawable/abc_ic_menu_share_mtrl_alpha : reachable=true
@drawable/abc_ic_search_api_material : reachable=false
    @attr/colorControlNormal
@drawable/abc_ic_voice_search_api_material : reachable=false
    @attr/colorControlNormal
@drawable/abc_item_background_holo_dark : reachable=false
    @drawable/abc_list_selector_disabled_holo_dark
    @drawable/abc_list_selector_background_transition_holo_dark
    @drawable/abc_list_focused_holo
@drawable/abc_item_background_holo_light : reachable=false
    @drawable/abc_list_selector_disabled_holo_light
    @drawable/abc_list_selector_background_transition_holo_light
    @drawable/abc_list_focused_holo
@drawable/abc_list_divider_material : reachable=false
@drawable/abc_list_divider_mtrl_alpha : reachable=true
@drawable/abc_list_focused_holo : reachable=false
@drawable/abc_list_longpressed_holo : reachable=false
@drawable/abc_list_pressed_holo_dark : reachable=false
@drawable/abc_list_pressed_holo_light : reachable=false
@drawable/abc_list_selector_background_transition_holo_dark : reachable=false
    @drawable/abc_list_pressed_holo_dark
    @drawable/abc_list_longpressed_holo
@drawable/abc_list_selector_background_transition_holo_light : reachable=false
    @drawable/abc_list_pressed_holo_light
    @drawable/abc_list_longpressed_holo
@drawable/abc_list_selector_disabled_holo_dark : reachable=false
@drawable/abc_list_selector_disabled_holo_light : reachable=false
@drawable/abc_list_selector_holo_dark : reachable=false
    @drawable/abc_list_selector_disabled_holo_dark
    @drawable/abc_list_selector_background_transition_holo_dark
    @drawable/abc_list_focused_holo
@drawable/abc_list_selector_holo_light : reachable=false
    @drawable/abc_list_selector_disabled_holo_light
    @drawable/abc_list_selector_background_transition_holo_light
    @drawable/abc_list_focused_holo
@drawable/abc_menu_hardkey_panel_mtrl_mult : reachable=true
@drawable/abc_popup_background_mtrl_mult : reachable=true
@drawable/abc_ratingbar_indicator_material : reachable=true
@drawable/abc_ratingbar_material : reachable=true
@drawable/abc_ratingbar_small_material : reachable=true
@drawable/abc_scrubber_control_off_mtrl_alpha : reachable=false
@drawable/abc_scrubber_control_to_pressed_mtrl_000 : reachable=false
@drawable/abc_scrubber_control_to_pressed_mtrl_005 : reachable=false
@drawable/abc_scrubber_primary_mtrl_alpha : reachable=false
@drawable/abc_scrubber_track_mtrl_alpha : reachable=false
@drawable/abc_seekbar_thumb_material : reachable=true
    @drawable/abc_scrubber_control_off_mtrl_alpha
    @drawable/abc_scrubber_control_to_pressed_mtrl_005
    @drawable/abc_scrubber_control_to_pressed_mtrl_000
@drawable/abc_seekbar_tick_mark_material : reachable=true
    @dimen/abc_progress_bar_height_material
@drawable/abc_seekbar_track_material : reachable=true
    @drawable/abc_scrubber_track_mtrl_alpha
    @drawable/abc_scrubber_primary_mtrl_alpha
@drawable/abc_spinner_mtrl_am_alpha : reachable=true
@drawable/abc_spinner_textfield_background_material : reachable=true
    @dimen/abc_control_inset_material
    @drawable/abc_textfield_default_mtrl_alpha
    @drawable/abc_spinner_mtrl_am_alpha
    @drawable/abc_textfield_activated_mtrl_alpha
@drawable/abc_star_black_48dp : reachable=true
@drawable/abc_star_half_black_48dp : reachable=true
@drawable/abc_switch_thumb_material : reachable=true
    @drawable/abc_btn_switch_to_on_mtrl_00012
    @drawable/abc_btn_switch_to_on_mtrl_00001
@drawable/abc_switch_track_mtrl_alpha : reachable=true
@drawable/abc_tab_indicator_material : reachable=true
    @drawable/abc_tab_indicator_mtrl_alpha
@drawable/abc_tab_indicator_mtrl_alpha : reachable=false
@drawable/abc_text_cursor_material : reachable=true
@drawable/abc_text_select_handle_left_mtrl : reachable=true
@drawable/abc_text_select_handle_middle_mtrl : reachable=true
@drawable/abc_text_select_handle_right_mtrl : reachable=true
@drawable/abc_textfield_activated_mtrl_alpha : reachable=true
@drawable/abc_textfield_default_mtrl_alpha : reachable=true
@drawable/abc_textfield_search_activated_mtrl_alpha : reachable=true
@drawable/abc_textfield_search_default_mtrl_alpha : reachable=true
@drawable/abc_textfield_search_material : reachable=true
    @drawable/abc_textfield_search_activated_mtrl_alpha
    @drawable/abc_textfield_search_default_mtrl_alpha
@drawable/abc_vector_test : reachable=true
@drawable/btn_checkbox_checked_mtrl : reachable=false
@drawable/btn_checkbox_checked_to_unchecked_mtrl_animation : reachable=false
    @drawable/btn_checkbox_checked_mtrl
    @anim/btn_checkbox_to_unchecked_icon_null_animation
    @anim/btn_checkbox_to_unchecked_check_path_merged_animation
    @anim/btn_checkbox_to_unchecked_box_inner_merged_animation
@drawable/btn_checkbox_unchecked_mtrl : reachable=false
@drawable/btn_checkbox_unchecked_to_checked_mtrl_animation : reachable=false
    @drawable/btn_checkbox_unchecked_mtrl
    @anim/btn_checkbox_to_checked_icon_null_animation
    @anim/btn_checkbox_to_checked_box_outer_merged_animation
    @anim/btn_checkbox_to_checked_box_inner_merged_animation
@drawable/btn_radio_off_mtrl : reachable=false
@drawable/btn_radio_off_to_on_mtrl_animation : reachable=false
    @drawable/btn_radio_off_mtrl
    @anim/btn_radio_to_on_mtrl_ring_outer_animation
    @anim/btn_radio_to_on_mtrl_ring_outer_path_animation
    @anim/btn_radio_to_on_mtrl_dot_group_animation
@drawable/btn_radio_on_mtrl : reachable=false
@drawable/btn_radio_on_to_off_mtrl_animation : reachable=false
    @drawable/btn_radio_on_mtrl
    @anim/btn_radio_to_off_mtrl_ring_outer_animation
    @anim/btn_radio_to_off_mtrl_ring_outer_path_animation
    @anim/btn_radio_to_off_mtrl_dot_group_animation
@drawable/launch_background : reachable=false
@drawable/notification_action_background : reachable=true
    @color/androidx_core_ripple_material_light
    @dimen/compat_button_inset_horizontal_material
    @dimen/compat_button_inset_vertical_material
    @dimen/compat_control_corner_material
    @dimen/compat_button_padding_vertical_material
    @dimen/compat_button_padding_horizontal_material
@drawable/notification_bg : reachable=true
    @drawable/notification_bg_normal_pressed
    @drawable/notification_bg_normal
@drawable/notification_bg_low : reachable=true
    @drawable/notification_bg_low_pressed
    @drawable/notification_bg_low_normal
@drawable/notification_bg_low_normal : reachable=true
@drawable/notification_bg_low_pressed : reachable=true
@drawable/notification_bg_normal : reachable=true
@drawable/notification_bg_normal_pressed : reachable=true
@drawable/notification_icon_background : reachable=true
    @color/notification_icon_bg_color
@drawable/notification_template_icon_bg : reachable=true
@drawable/notification_template_icon_low_bg : reachable=true
@drawable/notification_tile_bg : reachable=true
    @drawable/notify_panel_notification_icon_bg
@drawable/notify_panel_notification_icon_bg : reachable=true
@drawable/tooltip_frame_dark : reachable=true
    @color/tooltip_background_dark
    @dimen/tooltip_corner_radius
@drawable/tooltip_frame_light : reachable=true
    @color/tooltip_background_light
    @dimen/tooltip_corner_radius
@id/ALT : reachable=false
@id/CTRL : reachable=false
@id/FUNCTION : reachable=false
@id/META : reachable=false
@id/SHIFT : reachable=false
@id/SYM : reachable=false
@id/accessibility_action_clickable_span : reachable=true
@id/accessibility_custom_action_0 : reachable=true
@id/accessibility_custom_action_1 : reachable=true
@id/accessibility_custom_action_10 : reachable=true
@id/accessibility_custom_action_11 : reachable=true
@id/accessibility_custom_action_12 : reachable=true
@id/accessibility_custom_action_13 : reachable=true
@id/accessibility_custom_action_14 : reachable=true
@id/accessibility_custom_action_15 : reachable=true
@id/accessibility_custom_action_16 : reachable=true
@id/accessibility_custom_action_17 : reachable=true
@id/accessibility_custom_action_18 : reachable=true
@id/accessibility_custom_action_19 : reachable=true
@id/accessibility_custom_action_2 : reachable=true
@id/accessibility_custom_action_20 : reachable=true
@id/accessibility_custom_action_21 : reachable=true
@id/accessibility_custom_action_22 : reachable=true
@id/accessibility_custom_action_23 : reachable=true
@id/accessibility_custom_action_24 : reachable=true
@id/accessibility_custom_action_25 : reachable=true
@id/accessibility_custom_action_26 : reachable=true
@id/accessibility_custom_action_27 : reachable=true
@id/accessibility_custom_action_28 : reachable=true
@id/accessibility_custom_action_29 : reachable=true
@id/accessibility_custom_action_3 : reachable=true
@id/accessibility_custom_action_30 : reachable=true
@id/accessibility_custom_action_31 : reachable=true
@id/accessibility_custom_action_4 : reachable=true
@id/accessibility_custom_action_5 : reachable=true
@id/accessibility_custom_action_6 : reachable=true
@id/accessibility_custom_action_7 : reachable=true
@id/accessibility_custom_action_8 : reachable=true
@id/accessibility_custom_action_9 : reachable=true
@id/action_bar : reachable=true
@id/action_bar_activity_content : reachable=true
@id/action_bar_container : reachable=true
@id/action_bar_root : reachable=true
@id/action_bar_spinner : reachable=true
@id/action_bar_subtitle : reachable=true
@id/action_bar_title : reachable=true
@id/action_container : reachable=true
@id/action_context_bar : reachable=true
@id/action_divider : reachable=true
@id/action_image : reachable=true
@id/action_menu_divider : reachable=true
@id/action_menu_presenter : reachable=true
@id/action_mode_bar : reachable=true
@id/action_mode_bar_stub : reachable=true
@id/action_mode_close_button : reachable=true
@id/action_text : reachable=true
@id/actions : reachable=true
@id/activity_chooser_view_content : reachable=true
@id/add : reachable=false
@id/alertTitle : reachable=true
@id/always : reachable=false
@id/androidx_window_activity_scope : reachable=true
@id/async : reachable=false
@id/beginning : reachable=false
@id/blocking : reachable=false
@id/bottom : reachable=false
@id/buttonPanel : reachable=true
@id/center_vertical : reachable=false
@id/checkbox : reachable=false
@id/checked : reachable=false
@id/chronometer : reachable=false
@id/collapseActionView : reachable=false
@id/content : reachable=true
@id/contentPanel : reachable=true
@id/custom : reachable=true
@id/customPanel : reachable=true
@id/decor_content_parent : reachable=true
@id/default_activity_button : reachable=false
@id/dialog_button : reachable=false
@id/disableHome : reachable=false
@id/edit_query : reachable=true
@id/end : reachable=false
@id/expand_activities_button : reachable=false
@id/expanded_menu : reachable=false
@id/forever : reachable=false
@id/fragment_container_view_tag : reachable=true
@id/group_divider : reachable=true
@id/home : reachable=false
@id/homeAsUp : reachable=false
@id/icon : reachable=false
@id/icon_group : reachable=false
@id/ifRoom : reachable=false
@id/image : reachable=true
@id/info : reachable=true
@id/italic : reachable=true
@id/line1 : reachable=true
@id/line3 : reachable=true
@id/listMode : reachable=true
@id/list_item : reachable=true
@id/locale : reachable=true
@id/ltr : reachable=false
@id/message : reachable=true
@id/middle : reachable=false
@id/multiply : reachable=false
@id/never : reachable=false
@id/none : reachable=true
@id/normal : reachable=false
@id/notification_background : reachable=true
@id/notification_main_column : reachable=true
@id/notification_main_column_container : reachable=true
@id/off : reachable=true
@id/on : reachable=true
@id/parentPanel : reachable=true
@id/progress_circular : reachable=true
@id/progress_horizontal : reachable=true
@id/radio : reachable=false
@id/right_icon : reachable=false
@id/right_side : reachable=false
@id/rtl : reachable=false
@id/screen : reachable=false
@id/scrollIndicatorDown : reachable=true
@id/scrollIndicatorUp : reachable=true
@id/scrollView : reachable=true
@id/search_badge : reachable=false
@id/search_bar : reachable=false
@id/search_button : reachable=true
@id/search_close_btn : reachable=true
@id/search_edit_frame : reachable=true
@id/search_go_btn : reachable=true
@id/search_mag_icon : reachable=true
@id/search_plate : reachable=true
@id/search_src_text : reachable=true
@id/search_voice_btn : reachable=true
@id/select_dialog_listview : reachable=false
@id/shortcut : reachable=true
@id/showCustom : reachable=false
@id/showHome : reachable=false
@id/showTitle : reachable=false
@id/spacer : reachable=true
@id/special_effects_controller_view_tag : reachable=true
@id/split_action_bar : reachable=true
@id/src_atop : reachable=false
@id/src_in : reachable=false
@id/src_over : reachable=false
@id/submenuarrow : reachable=true
@id/submit_area : reachable=true
@id/tabMode : reachable=false
@id/tag_accessibility_actions : reachable=true
@id/tag_accessibility_clickable_spans : reachable=true
@id/tag_accessibility_heading : reachable=true
@id/tag_accessibility_pane_title : reachable=true
@id/tag_on_apply_window_listener : reachable=true
@id/tag_on_receive_content_listener : reachable=true
@id/tag_on_receive_content_mime_types : reachable=true
@id/tag_screen_reader_focusable : reachable=true
@id/tag_state_description : reachable=true
@id/tag_transition_group : reachable=false
@id/tag_unhandled_key_event_manager : reachable=true
@id/tag_unhandled_key_listeners : reachable=true
@id/tag_window_insets_animation_callback : reachable=true
@id/text : reachable=true
@id/text2 : reachable=true
@id/textSpacerNoButtons : reachable=true
@id/textSpacerNoTitle : reachable=true
@id/time : reachable=false
@id/title : reachable=true
@id/titleDividerNoCustom : reachable=true
@id/title_template : reachable=true
@id/top : reachable=true
@id/topPanel : reachable=true
@id/unchecked : reachable=false
@id/uniform : reachable=false
@id/up : reachable=false
@id/useLogo : reachable=false
@id/view_tree_lifecycle_owner : reachable=true
@id/view_tree_saved_state_registry_owner : reachable=true
@id/view_tree_view_model_store_owner : reachable=true
@id/visible_removing_fragment_view_tag : reachable=true
@id/withText : reachable=false
@id/wrap_content : reachable=false
@integer/abc_config_activityDefaultDur : reachable=false
@integer/abc_config_activityShortDur : reachable=false
@integer/cancel_button_image_alpha : reachable=true
@integer/config_tooltipAnimTime : reachable=false
@integer/status_bar_notification_info_maxnum : reachable=false
@interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 : reachable=false
@interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 : reachable=false
@interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 : reachable=false
@interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 : reachable=false
@interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 : reachable=false
@interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 : reachable=false
@interpolator/fast_out_slow_in : reachable=false
@layout/abc_action_bar_title_item : reachable=true
    @style/RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
    @dimen/abc_action_bar_subtitle_top_margin_material
@layout/abc_action_bar_up_container : reachable=false
    @attr/actionBarItemBackground
@layout/abc_action_menu_item_layout : reachable=true
    @attr/actionMenuTextAppearance
    @attr/actionMenuTextColor
    @attr/actionButtonStyle
@layout/abc_action_menu_layout : reachable=true
    @attr/actionBarDivider
@layout/abc_action_mode_bar : reachable=false
    @attr/actionModeStyle
    @attr/actionModeTheme
@layout/abc_action_mode_close_item_material : reachable=true
    @attr/actionModeCloseContentDescription
    @attr/actionModeCloseButtonStyle
    @attr/actionModeCloseDrawable
@layout/abc_activity_chooser_view : reachable=false
    @attr/activityChooserViewStyle
    @attr/actionBarItemBackground
@layout/abc_activity_chooser_view_list_item : reachable=false
    @attr/selectableItemBackground
    @attr/dropdownListPreferredItemHeight
    @attr/textAppearanceLargePopupMenu
@layout/abc_alert_dialog_button_bar_material : reachable=false
    @attr/buttonBarStyle
    @attr/buttonBarNeutralButtonStyle
    @attr/buttonBarNegativeButtonStyle
    @attr/buttonBarPositiveButtonStyle
@layout/abc_alert_dialog_material : reachable=false
    @layout/abc_alert_dialog_title_material
    @attr/colorControlHighlight
    @dimen/abc_dialog_padding_top_material
    @attr/dialogPreferredPadding
    @style/TextAppearance_AppCompat_Subhead
    @layout/abc_alert_dialog_button_bar_material
@layout/abc_alert_dialog_title_material : reachable=false
    @attr/dialogPreferredPadding
    @dimen/abc_dialog_padding_top_material
    @dimen/abc_dialog_title_divider_material
@layout/abc_cascading_menu_item_layout : reachable=true
    @drawable/abc_list_divider_material
    @attr/dropdownListPreferredItemHeight
    @style/RtlOverlay_Widget_AppCompat_PopupMenuItem
    @attr/textAppearanceLargePopupMenu
    @style/RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
    @attr/textAppearanceSmallPopupMenu
    @style/RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
    @style/RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
@layout/abc_dialog_title_material : reachable=true
    @attr/dialogPreferredPadding
    @dimen/abc_dialog_padding_top_material
    @layout/abc_screen_content_include
@layout/abc_expanded_menu_layout : reachable=true
    @attr/panelMenuListWidth
@layout/abc_list_menu_item_checkbox : reachable=true
@layout/abc_list_menu_item_icon : reachable=true
@layout/abc_list_menu_item_layout : reachable=true
    @attr/listPreferredItemHeightSmall
    @attr/listPreferredItemPaddingLeft
    @attr/listPreferredItemPaddingRight
    @attr/textAppearanceListItemSmall
@layout/abc_list_menu_item_radio : reachable=true
@layout/abc_popup_menu_header_item_layout : reachable=true
    @attr/dropdownListPreferredItemHeight
    @attr/textAppearancePopupMenuHeader
@layout/abc_popup_menu_item_layout : reachable=true
    @drawable/abc_list_divider_material
    @attr/dropdownListPreferredItemHeight
    @style/RtlOverlay_Widget_AppCompat_PopupMenuItem
    @style/RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
    @attr/textAppearanceLargePopupMenu
    @style/RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
    @attr/textAppearanceSmallPopupMenu
    @style/RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
@layout/abc_screen_content_include : reachable=false
@layout/abc_screen_simple : reachable=true
    @layout/abc_action_mode_bar
    @layout/abc_screen_content_include
@layout/abc_screen_simple_overlay_action_mode : reachable=true
    @layout/abc_screen_content_include
    @layout/abc_action_mode_bar
@layout/abc_screen_toolbar : reachable=true
    @layout/abc_screen_content_include
    @attr/actionBarStyle
    @attr/toolbarStyle
    @string/abc_action_bar_up_description
    @attr/actionModeStyle
    @attr/actionModeTheme
@layout/abc_search_dropdown_item_icons_2line : reachable=true
    @style/RtlOverlay_Widget_AppCompat_Search_DropDown
    @dimen/abc_dropdownitem_icon_width
    @style/RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
    @attr/selectableItemBackground
    @style/RtlOverlay_Widget_AppCompat_Search_DropDown_Query
    @style/RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
    @attr/textAppearanceSearchResultSubtitle
    @attr/textAppearanceSearchResultTitle
@layout/abc_search_view : reachable=true
    @string/abc_searchview_description_search
    @attr/actionButtonStyle
    @dimen/abc_dropdownitem_icon_width
    @style/RtlOverlay_Widget_AppCompat_SearchView_MagIcon
    @dimen/abc_dropdownitem_text_padding_right
    @dimen/abc_dropdownitem_text_padding_left
    @string/abc_searchview_description_clear
    @attr/selectableItemBackgroundBorderless
    @string/abc_searchview_description_submit
    @string/abc_searchview_description_voice
@layout/abc_select_dialog_material : reachable=false
    @dimen/abc_dialog_list_padding_bottom_no_buttons
    @dimen/abc_dialog_list_padding_top_no_title
    @attr/listDividerAlertDialog
    @style/Widget_AppCompat_ListView
@layout/abc_tooltip : reachable=true
    @dimen/tooltip_horizontal_padding
    @attr/tooltipForegroundColor
    @attr/tooltipFrameBackground
    @style/TextAppearance_AppCompat_Tooltip
    @dimen/tooltip_margin
    @dimen/tooltip_vertical_padding
@layout/custom_dialog : reachable=false
@layout/notification_action : reachable=true
    @style/Widget_Compat_NotificationActionContainer
    @dimen/notification_action_icon_size
    @style/Widget_Compat_NotificationActionText
@layout/notification_action_tombstone : reachable=true
    @style/Widget_Compat_NotificationActionContainer
    @dimen/notification_action_icon_size
    @style/Widget_Compat_NotificationActionText
@layout/notification_template_custom_big : reachable=true
    @layout/notification_template_icon_group
    @dimen/notification_large_icon_width
    @dimen/notification_large_icon_height
    @dimen/notification_right_side_padding_top
    @layout/notification_template_part_time
    @layout/notification_template_part_chronometer
    @style/TextAppearance_Compat_Notification_Info
@layout/notification_template_icon_group : reachable=true
    @dimen/notification_large_icon_width
    @dimen/notification_large_icon_height
    @dimen/notification_big_circle_margin
    @dimen/notification_right_icon_size
@layout/notification_template_part_chronometer : reachable=true
    @style/TextAppearance_Compat_Notification_Time
@layout/notification_template_part_time : reachable=true
    @style/TextAppearance_Compat_Notification_Time
@layout/select_dialog_item_material : reachable=false
    @attr/listPreferredItemPaddingRight
    @attr/listPreferredItemHeightSmall
    @attr/textAppearanceListItemSmall
    @attr/textColorAlertDialogListItem
    @attr/listPreferredItemPaddingLeft
@layout/select_dialog_multichoice_material : reachable=false
    @attr/dialogPreferredPadding
    @attr/textColorAlertDialogListItem
    @dimen/abc_select_dialog_padding_start_material
    @attr/listPreferredItemHeightSmall
@layout/select_dialog_singlechoice_material : reachable=false
    @attr/dialogPreferredPadding
    @attr/textColorAlertDialogListItem
    @dimen/abc_select_dialog_padding_start_material
    @attr/listPreferredItemHeightSmall
@layout/support_simple_spinner_dropdown_item : reachable=true
    @attr/dropdownListPreferredItemHeight
    @attr/spinnerDropDownItemStyle
@mipmap/ic_launcher : reachable=true
@string/abc_action_bar_home_description : reachable=false
@string/abc_action_bar_up_description : reachable=true
@string/abc_action_menu_overflow_description : reachable=false
@string/abc_action_mode_done : reachable=false
@string/abc_activity_chooser_view_see_all : reachable=false
@string/abc_activitychooserview_choose_application : reachable=false
@string/abc_capital_off : reachable=false
@string/abc_capital_on : reachable=false
@string/abc_menu_alt_shortcut_label : reachable=true
@string/abc_menu_ctrl_shortcut_label : reachable=true
@string/abc_menu_delete_shortcut_label : reachable=true
@string/abc_menu_enter_shortcut_label : reachable=true
@string/abc_menu_function_shortcut_label : reachable=true
@string/abc_menu_meta_shortcut_label : reachable=true
@string/abc_menu_shift_shortcut_label : reachable=true
@string/abc_menu_space_shortcut_label : reachable=true
@string/abc_menu_sym_shortcut_label : reachable=true
@string/abc_prepend_shortcut_label : reachable=true
@string/abc_search_hint : reachable=false
@string/abc_searchview_description_clear : reachable=false
@string/abc_searchview_description_query : reachable=false
@string/abc_searchview_description_search : reachable=true
@string/abc_searchview_description_submit : reachable=false
@string/abc_searchview_description_voice : reachable=false
@string/abc_shareactionprovider_share_with : reachable=false
@string/abc_shareactionprovider_share_with_application : reachable=false
@string/abc_toolbar_collapse_description : reachable=false
@string/search_menu_title : reachable=false
@string/status_bar_notification_info_overflow : reachable=false
@style/AlertDialog_AppCompat : reachable=false
    @style/Base_AlertDialog_AppCompat
@style/AlertDialog_AppCompat_Light : reachable=false
    @style/Base_AlertDialog_AppCompat_Light
@style/Animation_AppCompat_Dialog : reachable=false
    @style/Base_Animation_AppCompat_Dialog
@style/Animation_AppCompat_DropDownUp : reachable=false
    @style/Base_Animation_AppCompat_DropDownUp
@style/Animation_AppCompat_Tooltip : reachable=true
    @style/Base_Animation_AppCompat_Tooltip
@style/Base_AlertDialog_AppCompat : reachable=false
    @layout/abc_alert_dialog_material
    @layout/abc_select_dialog_material
    @attr/listLayout
    @layout/select_dialog_item_material
    @attr/listItemLayout
    @layout/select_dialog_multichoice_material
    @attr/multiChoiceItemLayout
    @layout/select_dialog_singlechoice_material
    @attr/singleChoiceItemLayout
    @dimen/abc_alert_dialog_button_dimen
    @attr/buttonIconDimen
@style/Base_AlertDialog_AppCompat_Light : reachable=false
    @style/Base_AlertDialog_AppCompat
@style/Base_Animation_AppCompat_Dialog : reachable=false
    @anim/abc_popup_enter
    @anim/abc_popup_exit
@style/Base_Animation_AppCompat_DropDownUp : reachable=false
    @anim/abc_grow_fade_in_from_bottom
    @anim/abc_shrink_fade_out_from_bottom
@style/Base_Animation_AppCompat_Tooltip : reachable=false
    @anim/abc_tooltip_enter
    @anim/abc_tooltip_exit
@style/Base_DialogWindowTitleBackground_AppCompat : reachable=false
    @attr/dialogPreferredPadding
    @dimen/abc_dialog_padding_top_material
@style/Base_DialogWindowTitle_AppCompat : reachable=false
    @style/TextAppearance_AppCompat_Title
@style/Base_TextAppearance_AppCompat : reachable=false
@style/Base_TextAppearance_AppCompat_Body1 : reachable=false
@style/Base_TextAppearance_AppCompat_Body2 : reachable=false
@style/Base_TextAppearance_AppCompat_Button : reachable=false
@style/Base_TextAppearance_AppCompat_Caption : reachable=false
@style/Base_TextAppearance_AppCompat_Display1 : reachable=false
@style/Base_TextAppearance_AppCompat_Display2 : reachable=false
@style/Base_TextAppearance_AppCompat_Display3 : reachable=false
@style/Base_TextAppearance_AppCompat_Display4 : reachable=false
@style/Base_TextAppearance_AppCompat_Headline : reachable=false
@style/Base_TextAppearance_AppCompat_Inverse : reachable=false
@style/Base_TextAppearance_AppCompat_Large : reachable=false
@style/Base_TextAppearance_AppCompat_Large_Inverse : reachable=false
@style/Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large : reachable=false
@style/Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small : reachable=false
@style/Base_TextAppearance_AppCompat_Medium : reachable=false
@style/Base_TextAppearance_AppCompat_Medium_Inverse : reachable=false
@style/Base_TextAppearance_AppCompat_Menu : reachable=false
@style/Base_TextAppearance_AppCompat_SearchResult : reachable=false
@style/Base_TextAppearance_AppCompat_SearchResult_Subtitle : reachable=false
@style/Base_TextAppearance_AppCompat_SearchResult_Title : reachable=false
@style/Base_TextAppearance_AppCompat_Small : reachable=false
@style/Base_TextAppearance_AppCompat_Small_Inverse : reachable=false
@style/Base_TextAppearance_AppCompat_Subhead : reachable=false
@style/Base_TextAppearance_AppCompat_Subhead_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Subhead
@style/Base_TextAppearance_AppCompat_Title : reachable=false
@style/Base_TextAppearance_AppCompat_Title_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Title
@style/Base_TextAppearance_AppCompat_Tooltip : reachable=false
    @style/Base_TextAppearance_AppCompat
@style/Base_TextAppearance_AppCompat_Widget_ActionBar_Menu : reachable=false
    @style/TextAppearance_AppCompat_Button
    @attr/actionMenuTextColor
    @bool/abc_config_actionMenuItemAllCaps
    @attr/textAllCaps
@style/Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_ActionBar_Title : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_ActionMode_Title : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_Button : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_Button
    @color/abc_btn_colored_borderless_text_material
@style/Base_TextAppearance_AppCompat_Widget_Button_Colored : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_Button
    @color/abc_btn_colored_text_material
@style/Base_TextAppearance_AppCompat_Widget_Button_Inverse : reachable=false
    @style/TextAppearance_AppCompat_Button
@style/Base_TextAppearance_AppCompat_Widget_DropDownItem : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_PopupMenu_Header : reachable=false
    @style/TextAppearance_AppCompat
    @dimen/abc_text_size_menu_header_material
@style/Base_TextAppearance_AppCompat_Widget_PopupMenu_Large : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_PopupMenu_Small : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_Switch : reachable=false
@style/Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem : reachable=false
@style/Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item : reachable=false
@style/Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle : reachable=false
@style/Base_TextAppearance_Widget_AppCompat_Toolbar_Title : reachable=false
@style/Base_ThemeOverlay_AppCompat : reachable=false
    @style/Platform_ThemeOverlay_AppCompat
@style/Base_ThemeOverlay_AppCompat_ActionBar : reachable=false
    @style/Base_ThemeOverlay_AppCompat
    @attr/colorControlNormal
    @style/Widget_AppCompat_SearchView_ActionBar
    @attr/searchViewStyle
@style/Base_ThemeOverlay_AppCompat_Dark : reachable=false
    @style/Platform_ThemeOverlay_AppCompat_Dark
    @color/background_material_dark
    @color/foreground_material_dark
    @color/foreground_material_light
    @color/background_floating_material_dark
    @attr/colorBackgroundFloating
    @color/abc_primary_text_material_dark
    @color/abc_primary_text_material_light
    @color/abc_primary_text_disable_only_material_dark
    @color/abc_secondary_text_material_dark
    @color/abc_secondary_text_material_light
    @color/abc_hint_foreground_material_dark
    @color/abc_hint_foreground_material_light
    @color/highlighted_text_material_dark
    @attr/colorControlNormal
    @color/ripple_material_dark
    @attr/colorControlHighlight
    @color/button_material_dark
    @attr/colorButtonNormal
    @color/switch_thumb_material_dark
    @attr/colorSwitchThumbNormal
    @attr/isLightTheme
    @color/abc_background_cache_hint_selector_material_dark
@style/Base_ThemeOverlay_AppCompat_Dark_ActionBar : reachable=false
    @style/Base_ThemeOverlay_AppCompat_Dark
    @attr/colorControlNormal
    @style/Widget_AppCompat_SearchView_ActionBar
    @attr/searchViewStyle
@style/Base_ThemeOverlay_AppCompat_Dialog : reachable=false
    @style/Base_V21_ThemeOverlay_AppCompat_Dialog
@style/Base_ThemeOverlay_AppCompat_Dialog_Alert : reachable=false
    @style/Base_ThemeOverlay_AppCompat_Dialog
    @dimen/abc_dialog_min_width_major
    @dimen/abc_dialog_min_width_minor
@style/Base_ThemeOverlay_AppCompat_Light : reachable=false
    @style/Platform_ThemeOverlay_AppCompat_Light
    @color/background_material_light
    @color/foreground_material_light
    @color/foreground_material_dark
    @color/background_floating_material_light
    @attr/colorBackgroundFloating
    @color/abc_primary_text_material_light
    @color/abc_primary_text_material_dark
    @color/abc_secondary_text_material_light
    @color/abc_secondary_text_material_dark
    @color/abc_primary_text_disable_only_material_light
    @color/abc_hint_foreground_material_light
    @color/abc_hint_foreground_material_dark
    @color/highlighted_text_material_light
    @attr/colorControlNormal
    @color/ripple_material_light
    @attr/colorControlHighlight
    @color/button_material_light
    @attr/colorButtonNormal
    @color/switch_thumb_material_light
    @attr/colorSwitchThumbNormal
    @attr/isLightTheme
    @color/abc_primary_text_disable_only_material_dark
    @color/abc_background_cache_hint_selector_material_light
@style/Base_Theme_AppCompat : reachable=false
    @style/Base_V21_Theme_AppCompat
    @style/Base_V22_Theme_AppCompat
    @style/Base_V23_Theme_AppCompat
    @style/Base_V26_Theme_AppCompat
    @style/Base_V28_Theme_AppCompat
@style/Base_Theme_AppCompat_CompactMenu : reachable=false
    @style/Widget_AppCompat_ListView_Menu
    @style/Animation_AppCompat_DropDownUp
@style/Base_Theme_AppCompat_Dialog : reachable=false
    @style/Base_V21_Theme_AppCompat_Dialog
@style/Base_Theme_AppCompat_DialogWhenLarge : reachable=false
    @style/Theme_AppCompat
    @style/Base_Theme_AppCompat_Dialog_FixedSize
@style/Base_Theme_AppCompat_Dialog_Alert : reachable=false
    @style/Base_Theme_AppCompat_Dialog
    @dimen/abc_dialog_min_width_major
    @dimen/abc_dialog_min_width_minor
@style/Base_Theme_AppCompat_Dialog_FixedSize : reachable=false
    @style/Base_Theme_AppCompat_Dialog
    @dimen/abc_dialog_fixed_width_major
    @attr/windowFixedWidthMajor
    @dimen/abc_dialog_fixed_width_minor
    @attr/windowFixedWidthMinor
    @dimen/abc_dialog_fixed_height_major
    @attr/windowFixedHeightMajor
    @dimen/abc_dialog_fixed_height_minor
    @attr/windowFixedHeightMinor
@style/Base_Theme_AppCompat_Dialog_MinWidth : reachable=false
    @style/Base_Theme_AppCompat_Dialog
    @dimen/abc_dialog_min_width_major
    @dimen/abc_dialog_min_width_minor
@style/Base_Theme_AppCompat_Light : reachable=false
    @style/Base_V21_Theme_AppCompat_Light
    @style/Base_V22_Theme_AppCompat_Light
    @style/Base_V23_Theme_AppCompat_Light
    @style/Base_V26_Theme_AppCompat_Light
    @style/Base_V28_Theme_AppCompat_Light
@style/Base_Theme_AppCompat_Light_DarkActionBar : reachable=false
    @style/Base_Theme_AppCompat_Light
    @style/ThemeOverlay_AppCompat_Light
    @attr/actionBarPopupTheme
    @attr/actionBarWidgetTheme
    @style/ThemeOverlay_AppCompat_Dark_ActionBar
    @attr/actionBarTheme
    @attr/actionModeTheme
    @drawable/abc_list_selector_holo_dark
    @attr/listChoiceBackgroundIndicator
    @color/primary_dark_material_dark
    @attr/colorPrimaryDark
    @color/primary_material_dark
    @attr/colorPrimary
@style/Base_Theme_AppCompat_Light_Dialog : reachable=false
    @style/Base_V21_Theme_AppCompat_Light_Dialog
@style/Base_Theme_AppCompat_Light_DialogWhenLarge : reachable=false
    @style/Theme_AppCompat_Light
    @style/Base_Theme_AppCompat_Light_Dialog_FixedSize
@style/Base_Theme_AppCompat_Light_Dialog_Alert : reachable=false
    @style/Base_Theme_AppCompat_Light_Dialog
    @dimen/abc_dialog_min_width_major
    @dimen/abc_dialog_min_width_minor
@style/Base_Theme_AppCompat_Light_Dialog_FixedSize : reachable=false
    @style/Base_Theme_AppCompat_Light_Dialog
    @dimen/abc_dialog_fixed_width_major
    @attr/windowFixedWidthMajor
    @dimen/abc_dialog_fixed_width_minor
    @attr/windowFixedWidthMinor
    @dimen/abc_dialog_fixed_height_major
    @attr/windowFixedHeightMajor
    @dimen/abc_dialog_fixed_height_minor
    @attr/windowFixedHeightMinor
@style/Base_Theme_AppCompat_Light_Dialog_MinWidth : reachable=false
    @style/Base_Theme_AppCompat_Light_Dialog
    @dimen/abc_dialog_min_width_major
    @dimen/abc_dialog_min_width_minor
@style/Base_V21_ThemeOverlay_AppCompat_Dialog : reachable=false
    @style/Base_V7_ThemeOverlay_AppCompat_Dialog
    @dimen/abc_floating_window_z
@style/Base_V21_Theme_AppCompat : reachable=false
    @style/Base_V7_Theme_AppCompat
    @attr/actionBarSize
    @attr/actionBarDivider
    @drawable/abc_action_bar_item_background_material
    @attr/actionBarItemBackground
    @attr/actionButtonStyle
    @attr/actionModeBackground
    @attr/actionModeCloseDrawable
    @attr/homeAsUpIndicator
    @attr/listPreferredItemHeightSmall
    @attr/textAppearanceLargePopupMenu
    @attr/textAppearanceSmallPopupMenu
    @attr/selectableItemBackground
    @attr/selectableItemBackgroundBorderless
    @attr/borderlessButtonStyle
    @attr/dividerHorizontal
    @attr/dividerVertical
    @drawable/abc_edit_text_material
    @attr/editTextBackground
    @attr/editTextColor
    @attr/listChoiceBackgroundIndicator
    @attr/buttonStyle
    @attr/buttonStyleSmall
    @attr/checkboxStyle
    @attr/checkedTextViewStyle
    @attr/radioButtonStyle
    @attr/ratingBarStyle
    @attr/spinnerStyle
    @attr/colorPrimary
    @attr/colorPrimaryDark
    @attr/colorAccent
    @attr/colorControlNormal
    @attr/colorControlActivated
    @attr/colorControlHighlight
    @attr/colorButtonNormal
@style/Base_V21_Theme_AppCompat_Dialog : reachable=false
    @style/Base_V7_Theme_AppCompat_Dialog
    @dimen/abc_floating_window_z
@style/Base_V21_Theme_AppCompat_Light : reachable=false
    @style/Base_V7_Theme_AppCompat_Light
    @attr/actionBarSize
    @attr/actionBarDivider
    @drawable/abc_action_bar_item_background_material
    @attr/actionBarItemBackground
    @attr/actionButtonStyle
    @attr/actionModeBackground
    @attr/actionModeCloseDrawable
    @attr/homeAsUpIndicator
    @attr/listPreferredItemHeightSmall
    @attr/textAppearanceLargePopupMenu
    @attr/textAppearanceSmallPopupMenu
    @attr/selectableItemBackground
    @attr/selectableItemBackgroundBorderless
    @attr/borderlessButtonStyle
    @attr/dividerHorizontal
    @attr/dividerVertical
    @drawable/abc_edit_text_material
    @attr/editTextBackground
    @attr/editTextColor
    @attr/listChoiceBackgroundIndicator
    @attr/buttonStyle
    @attr/buttonStyleSmall
    @attr/checkboxStyle
    @attr/checkedTextViewStyle
    @attr/radioButtonStyle
    @attr/ratingBarStyle
    @attr/spinnerStyle
    @attr/colorPrimary
    @attr/colorPrimaryDark
    @attr/colorAccent
    @attr/colorControlNormal
    @attr/colorControlActivated
    @attr/colorControlHighlight
    @attr/colorButtonNormal
@style/Base_V21_Theme_AppCompat_Light_Dialog : reachable=false
    @style/Base_V7_Theme_AppCompat_Light_Dialog
    @dimen/abc_floating_window_z
@style/Base_V22_Theme_AppCompat : reachable=false
    @style/Base_V21_Theme_AppCompat
    @attr/actionModeShareDrawable
    @attr/editTextBackground
@style/Base_V22_Theme_AppCompat_Light : reachable=false
    @style/Base_V21_Theme_AppCompat_Light
    @attr/actionModeShareDrawable
    @attr/editTextBackground
@style/Base_V23_Theme_AppCompat : reachable=false
    @style/Base_V22_Theme_AppCompat
    @attr/ratingBarStyleIndicator
    @attr/ratingBarStyleSmall
    @attr/actionBarItemBackground
    @attr/actionMenuTextColor
    @attr/actionMenuTextAppearance
    @attr/actionOverflowButtonStyle
    @drawable/abc_control_background_material
    @attr/controlBackground
@style/Base_V23_Theme_AppCompat_Light : reachable=false
    @style/Base_V22_Theme_AppCompat_Light
    @attr/ratingBarStyleIndicator
    @attr/ratingBarStyleSmall
    @attr/actionBarItemBackground
    @attr/actionMenuTextColor
    @attr/actionMenuTextAppearance
    @attr/actionOverflowButtonStyle
    @drawable/abc_control_background_material
    @attr/controlBackground
@style/Base_V26_Theme_AppCompat : reachable=false
    @style/Base_V23_Theme_AppCompat
    @attr/colorError
@style/Base_V26_Theme_AppCompat_Light : reachable=false
    @style/Base_V23_Theme_AppCompat_Light
    @attr/colorError
@style/Base_V26_Widget_AppCompat_Toolbar : reachable=false
    @style/Base_V7_Widget_AppCompat_Toolbar
@style/Base_V28_Theme_AppCompat : reachable=false
    @style/Base_V26_Theme_AppCompat
    @attr/dialogCornerRadius
@style/Base_V28_Theme_AppCompat_Light : reachable=false
    @style/Base_V26_Theme_AppCompat_Light
    @attr/dialogCornerRadius
@style/Base_V7_ThemeOverlay_AppCompat_Dialog : reachable=false
    @style/Base_ThemeOverlay_AppCompat
    @attr/colorBackgroundFloating
    @style/RtlOverlay_DialogWindowTitle_AppCompat
    @style/Base_DialogWindowTitleBackground_AppCompat
    @drawable/abc_dialog_material_background
    @style/Animation_AppCompat_Dialog
    @attr/windowActionBar
    @attr/windowActionModeOverlay
    @attr/listPreferredItemPaddingLeft
    @attr/listPreferredItemPaddingRight
    @attr/windowFixedWidthMajor
    @attr/windowFixedWidthMinor
    @attr/windowFixedHeightMajor
    @attr/windowFixedHeightMinor
    @style/Widget_AppCompat_ButtonBar_AlertDialog
    @style/Widget_AppCompat_Button_Borderless
@style/Base_V7_Theme_AppCompat : reachable=false
    @style/Platform_AppCompat
    @attr/windowNoTitle
    @attr/windowActionBar
    @attr/windowActionBarOverlay
    @attr/windowActionModeOverlay
    @attr/actionBarPopupTheme
    @color/background_floating_material_dark
    @attr/colorBackgroundFloating
    @attr/isLightTheme
    @drawable/abc_item_background_holo_dark
    @attr/selectableItemBackground
    @attr/selectableItemBackgroundBorderless
    @style/Widget_AppCompat_Button_Borderless
    @attr/borderlessButtonStyle
    @drawable/abc_ic_ab_back_material
    @attr/homeAsUpIndicator
    @drawable/abc_list_divider_mtrl_alpha
    @attr/dividerVertical
    @attr/dividerHorizontal
    @style/Widget_AppCompat_ActionBar_TabView
    @attr/actionBarTabStyle
    @style/Widget_AppCompat_ActionBar_TabBar
    @attr/actionBarTabBarStyle
    @style/Widget_AppCompat_ActionBar_TabText
    @attr/actionBarTabTextStyle
    @style/Widget_AppCompat_ActionButton
    @attr/actionButtonStyle
    @style/Widget_AppCompat_ActionButton_Overflow
    @attr/actionOverflowButtonStyle
    @style/Widget_AppCompat_PopupMenu_Overflow
    @attr/actionOverflowMenuStyle
    @style/Widget_AppCompat_ActionBar_Solid
    @attr/actionBarStyle
    @attr/actionBarSplitStyle
    @attr/actionBarWidgetTheme
    @style/ThemeOverlay_AppCompat_ActionBar
    @attr/actionBarTheme
    @dimen/abc_action_bar_default_height_material
    @attr/actionBarSize
    @attr/actionBarDivider
    @attr/actionBarItemBackground
    @style/TextAppearance_AppCompat_Widget_ActionBar_Menu
    @attr/actionMenuTextAppearance
    @attr/actionMenuTextColor
    @style/Widget_AppCompat_Spinner_DropDown_ActionBar
    @attr/actionDropDownStyle
    @attr/actionModeTheme
    @style/Widget_AppCompat_ActionMode
    @attr/actionModeStyle
    @drawable/abc_cab_background_top_material
    @attr/actionModeBackground
    @attr/colorPrimaryDark
    @attr/actionModeSplitBackground
    @string/abc_action_mode_done
    @attr/actionModeCloseContentDescription
    @attr/actionModeCloseDrawable
    @style/Widget_AppCompat_ActionButton_CloseMode
    @attr/actionModeCloseButtonStyle
    @drawable/abc_ic_menu_cut_mtrl_alpha
    @attr/actionModeCutDrawable
    @drawable/abc_ic_menu_copy_mtrl_am_alpha
    @attr/actionModeCopyDrawable
    @drawable/abc_ic_menu_paste_mtrl_am_alpha
    @attr/actionModePasteDrawable
    @drawable/abc_ic_menu_selectall_mtrl_alpha
    @attr/actionModeSelectAllDrawable
    @drawable/abc_ic_menu_share_mtrl_alpha
    @attr/actionModeShareDrawable
    @dimen/abc_panel_menu_list_width
    @attr/panelMenuListWidth
    @style/Theme_AppCompat_CompactMenu
    @attr/panelMenuListTheme
    @drawable/abc_menu_hardkey_panel_mtrl_mult
    @attr/panelBackground
    @drawable/abc_list_selector_holo_dark
    @attr/listChoiceBackgroundIndicator
    @style/TextAppearance_AppCompat_Subhead
    @attr/textAppearanceListItem
    @attr/textAppearanceListItemSmall
    @style/TextAppearance_AppCompat_Body1
    @attr/textAppearanceListItemSecondary
    @dimen/abc_list_item_height_material
    @attr/listPreferredItemHeight
    @dimen/abc_list_item_height_small_material
    @attr/listPreferredItemHeightSmall
    @dimen/abc_list_item_height_large_material
    @attr/listPreferredItemHeightLarge
    @dimen/abc_list_item_padding_horizontal_material
    @attr/listPreferredItemPaddingLeft
    @attr/listPreferredItemPaddingRight
    @attr/listPreferredItemPaddingStart
    @attr/listPreferredItemPaddingEnd
    @style/Widget_AppCompat_Spinner
    @attr/spinnerStyle
    @style/Widget_AppCompat_TextView_SpinnerItem
    @style/Widget_AppCompat_ListView_DropDown
    @style/Widget_AppCompat_DropDownItem_Spinner
    @attr/spinnerDropDownItemStyle
    @attr/dropdownListPreferredItemHeight
    @style/Widget_AppCompat_PopupMenu
    @attr/popupMenuStyle
    @style/TextAppearance_AppCompat_Widget_PopupMenu_Large
    @attr/textAppearanceLargePopupMenu
    @style/TextAppearance_AppCompat_Widget_PopupMenu_Small
    @attr/textAppearanceSmallPopupMenu
    @style/TextAppearance_AppCompat_Widget_PopupMenu_Header
    @attr/textAppearancePopupMenuHeader
    @style/Widget_AppCompat_ListPopupWindow
    @attr/listPopupWindowStyle
    @attr/dropDownListViewStyle
    @style/Widget_AppCompat_ListMenuView
    @attr/listMenuViewStyle
    @style/Widget_AppCompat_SearchView
    @attr/searchViewStyle
    @color/abc_search_url_text
    @attr/textColorSearchUrl
    @style/TextAppearance_AppCompat_SearchResult_Title
    @attr/textAppearanceSearchResultTitle
    @style/TextAppearance_AppCompat_SearchResult_Subtitle
    @attr/textAppearanceSearchResultSubtitle
    @style/Widget_AppCompat_ActivityChooserView
    @attr/activityChooserViewStyle
    @style/Widget_AppCompat_Toolbar
    @attr/toolbarStyle
    @style/Widget_AppCompat_Toolbar_Button_Navigation
    @attr/toolbarNavigationButtonStyle
    @style/Widget_AppCompat_EditText
    @attr/editTextStyle
    @drawable/abc_edit_text_material
    @attr/editTextBackground
    @attr/editTextColor
    @style/Widget_AppCompat_AutoCompleteTextView
    @attr/autoCompleteTextViewStyle
    @style/Widget_AppCompat_TextView
    @color/primary_dark_material_dark
    @color/primary_material_dark
    @attr/colorPrimary
    @color/accent_material_dark
    @attr/colorAccent
    @attr/colorControlNormal
    @attr/colorControlActivated
    @color/ripple_material_dark
    @attr/colorControlHighlight
    @color/button_material_dark
    @attr/colorButtonNormal
    @color/switch_thumb_material_dark
    @attr/colorSwitchThumbNormal
    @attr/controlBackground
    @style/Widget_AppCompat_DrawerArrowToggle
    @attr/drawerArrowStyle
    @style/Widget_AppCompat_CompoundButton_CheckBox
    @attr/checkboxStyle
    @style/Widget_AppCompat_CompoundButton_RadioButton
    @attr/radioButtonStyle
    @style/Widget_AppCompat_CompoundButton_Switch
    @attr/switchStyle
    @style/Widget_AppCompat_RatingBar
    @attr/ratingBarStyle
    @style/Widget_AppCompat_RatingBar_Indicator
    @attr/ratingBarStyleIndicator
    @style/Widget_AppCompat_RatingBar_Small
    @attr/ratingBarStyleSmall
    @style/Widget_AppCompat_SeekBar
    @attr/seekBarStyle
    @style/Widget_AppCompat_Button
    @attr/buttonStyle
    @style/Widget_AppCompat_Button_Small
    @attr/buttonStyleSmall
    @style/TextAppearance_AppCompat_Widget_Button
    @style/Widget_AppCompat_ImageButton
    @attr/imageButtonStyle
    @style/Widget_AppCompat_ButtonBar
    @attr/buttonBarStyle
    @style/Widget_AppCompat_Button_ButtonBar_AlertDialog
    @attr/buttonBarButtonStyle
    @attr/buttonBarPositiveButtonStyle
    @attr/buttonBarNegativeButtonStyle
    @attr/buttonBarNeutralButtonStyle
    @style/ThemeOverlay_AppCompat_Dialog
    @attr/dialogTheme
    @dimen/abc_dialog_padding_material
    @attr/dialogPreferredPadding
    @dimen/abc_dialog_corner_radius_material
    @attr/dialogCornerRadius
    @style/ThemeOverlay_AppCompat_Dialog_Alert
    @attr/alertDialogTheme
    @style/AlertDialog_AppCompat
    @attr/alertDialogStyle
    @attr/alertDialogCenterButtons
    @color/abc_primary_text_material_dark
    @attr/textColorAlertDialogListItem
    @attr/listDividerAlertDialog
    @attr/windowFixedWidthMajor
    @attr/windowFixedWidthMinor
    @attr/windowFixedHeightMajor
    @attr/windowFixedHeightMinor
    @drawable/tooltip_frame_light
    @attr/tooltipFrameBackground
    @color/foreground_material_light
    @attr/tooltipForegroundColor
    @color/error_color_material_dark
    @attr/colorError
@style/Base_V7_Theme_AppCompat_Dialog : reachable=false
    @style/Base_Theme_AppCompat
    @attr/colorBackgroundFloating
    @style/RtlOverlay_DialogWindowTitle_AppCompat
    @style/Base_DialogWindowTitleBackground_AppCompat
    @drawable/abc_dialog_material_background
    @style/Animation_AppCompat_Dialog
    @attr/windowActionBar
    @attr/windowActionModeOverlay
    @attr/listPreferredItemPaddingLeft
    @attr/listPreferredItemPaddingRight
    @style/Widget_AppCompat_ButtonBar_AlertDialog
    @style/Widget_AppCompat_Button_Borderless
@style/Base_V7_Theme_AppCompat_Light : reachable=false
    @style/Platform_AppCompat_Light
    @attr/windowNoTitle
    @attr/windowActionBar
    @attr/windowActionBarOverlay
    @attr/windowActionModeOverlay
    @attr/actionBarPopupTheme
    @color/background_floating_material_light
    @attr/colorBackgroundFloating
    @attr/isLightTheme
    @drawable/abc_item_background_holo_light
    @attr/selectableItemBackground
    @attr/selectableItemBackgroundBorderless
    @style/Widget_AppCompat_Button_Borderless
    @attr/borderlessButtonStyle
    @drawable/abc_ic_ab_back_material
    @attr/homeAsUpIndicator
    @drawable/abc_list_divider_mtrl_alpha
    @attr/dividerVertical
    @attr/dividerHorizontal
    @style/Widget_AppCompat_Light_ActionBar_TabView
    @attr/actionBarTabStyle
    @style/Widget_AppCompat_Light_ActionBar_TabBar
    @attr/actionBarTabBarStyle
    @style/Widget_AppCompat_Light_ActionBar_TabText
    @attr/actionBarTabTextStyle
    @style/Widget_AppCompat_Light_ActionButton
    @attr/actionButtonStyle
    @style/Widget_AppCompat_Light_ActionButton_Overflow
    @attr/actionOverflowButtonStyle
    @style/Widget_AppCompat_Light_PopupMenu_Overflow
    @attr/actionOverflowMenuStyle
    @style/Widget_AppCompat_Light_ActionBar_Solid
    @attr/actionBarStyle
    @attr/actionBarSplitStyle
    @attr/actionBarWidgetTheme
    @style/ThemeOverlay_AppCompat_ActionBar
    @attr/actionBarTheme
    @dimen/abc_action_bar_default_height_material
    @attr/actionBarSize
    @attr/actionBarDivider
    @attr/actionBarItemBackground
    @style/TextAppearance_AppCompat_Widget_ActionBar_Menu
    @attr/actionMenuTextAppearance
    @attr/actionMenuTextColor
    @attr/actionModeTheme
    @style/Widget_AppCompat_ActionMode
    @attr/actionModeStyle
    @drawable/abc_cab_background_top_material
    @attr/actionModeBackground
    @attr/colorPrimaryDark
    @attr/actionModeSplitBackground
    @string/abc_action_mode_done
    @attr/actionModeCloseContentDescription
    @attr/actionModeCloseDrawable
    @style/Widget_AppCompat_ActionButton_CloseMode
    @attr/actionModeCloseButtonStyle
    @drawable/abc_ic_menu_cut_mtrl_alpha
    @attr/actionModeCutDrawable
    @drawable/abc_ic_menu_copy_mtrl_am_alpha
    @attr/actionModeCopyDrawable
    @drawable/abc_ic_menu_paste_mtrl_am_alpha
    @attr/actionModePasteDrawable
    @drawable/abc_ic_menu_selectall_mtrl_alpha
    @attr/actionModeSelectAllDrawable
    @drawable/abc_ic_menu_share_mtrl_alpha
    @attr/actionModeShareDrawable
    @style/Widget_AppCompat_Light_Spinner_DropDown_ActionBar
    @attr/actionDropDownStyle
    @dimen/abc_panel_menu_list_width
    @attr/panelMenuListWidth
    @style/Theme_AppCompat_CompactMenu
    @attr/panelMenuListTheme
    @drawable/abc_menu_hardkey_panel_mtrl_mult
    @attr/panelBackground
    @drawable/abc_list_selector_holo_light
    @attr/listChoiceBackgroundIndicator
    @style/TextAppearance_AppCompat_Subhead
    @attr/textAppearanceListItem
    @attr/textAppearanceListItemSmall
    @style/TextAppearance_AppCompat_Body1
    @attr/textAppearanceListItemSecondary
    @dimen/abc_list_item_height_material
    @attr/listPreferredItemHeight
    @dimen/abc_list_item_height_small_material
    @attr/listPreferredItemHeightSmall
    @dimen/abc_list_item_height_large_material
    @attr/listPreferredItemHeightLarge
    @dimen/abc_list_item_padding_horizontal_material
    @attr/listPreferredItemPaddingLeft
    @attr/listPreferredItemPaddingRight
    @attr/listPreferredItemPaddingStart
    @attr/listPreferredItemPaddingEnd
    @style/Widget_AppCompat_Spinner
    @attr/spinnerStyle
    @style/Widget_AppCompat_TextView_SpinnerItem
    @style/Widget_AppCompat_ListView_DropDown
    @style/Widget_AppCompat_DropDownItem_Spinner
    @attr/spinnerDropDownItemStyle
    @attr/dropdownListPreferredItemHeight
    @style/Widget_AppCompat_Light_PopupMenu
    @attr/popupMenuStyle
    @style/TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
    @attr/textAppearanceLargePopupMenu
    @style/TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
    @attr/textAppearanceSmallPopupMenu
    @style/TextAppearance_AppCompat_Widget_PopupMenu_Header
    @attr/textAppearancePopupMenuHeader
    @style/Widget_AppCompat_ListPopupWindow
    @attr/listPopupWindowStyle
    @attr/dropDownListViewStyle
    @style/Widget_AppCompat_ListMenuView
    @attr/listMenuViewStyle
    @style/Widget_AppCompat_Light_SearchView
    @attr/searchViewStyle
    @color/abc_search_url_text
    @attr/textColorSearchUrl
    @style/TextAppearance_AppCompat_SearchResult_Title
    @attr/textAppearanceSearchResultTitle
    @style/TextAppearance_AppCompat_SearchResult_Subtitle
    @attr/textAppearanceSearchResultSubtitle
    @style/Widget_AppCompat_ActivityChooserView
    @attr/activityChooserViewStyle
    @style/Widget_AppCompat_Toolbar
    @attr/toolbarStyle
    @style/Widget_AppCompat_Toolbar_Button_Navigation
    @attr/toolbarNavigationButtonStyle
    @style/Widget_AppCompat_EditText
    @attr/editTextStyle
    @drawable/abc_edit_text_material
    @attr/editTextBackground
    @attr/editTextColor
    @style/Widget_AppCompat_AutoCompleteTextView
    @attr/autoCompleteTextViewStyle
    @style/Widget_AppCompat_TextView
    @color/primary_dark_material_light
    @color/primary_material_light
    @attr/colorPrimary
    @color/accent_material_light
    @attr/colorAccent
    @attr/colorControlNormal
    @attr/colorControlActivated
    @color/ripple_material_light
    @attr/colorControlHighlight
    @color/button_material_light
    @attr/colorButtonNormal
    @color/switch_thumb_material_light
    @attr/colorSwitchThumbNormal
    @attr/controlBackground
    @style/Widget_AppCompat_DrawerArrowToggle
    @attr/drawerArrowStyle
    @style/Widget_AppCompat_CompoundButton_CheckBox
    @attr/checkboxStyle
    @style/Widget_AppCompat_CompoundButton_RadioButton
    @attr/radioButtonStyle
    @style/Widget_AppCompat_CompoundButton_Switch
    @attr/switchStyle
    @style/Widget_AppCompat_RatingBar
    @attr/ratingBarStyle
    @style/Widget_AppCompat_RatingBar_Indicator
    @attr/ratingBarStyleIndicator
    @style/Widget_AppCompat_RatingBar_Small
    @attr/ratingBarStyleSmall
    @style/Widget_AppCompat_SeekBar
    @attr/seekBarStyle
    @style/Widget_AppCompat_Button
    @attr/buttonStyle
    @style/Widget_AppCompat_Button_Small
    @attr/buttonStyleSmall
    @style/TextAppearance_AppCompat_Widget_Button
    @style/Widget_AppCompat_ImageButton
    @attr/imageButtonStyle
    @style/Widget_AppCompat_ButtonBar
    @attr/buttonBarStyle
    @style/Widget_AppCompat_Button_ButtonBar_AlertDialog
    @attr/buttonBarButtonStyle
    @attr/buttonBarPositiveButtonStyle
    @attr/buttonBarNegativeButtonStyle
    @attr/buttonBarNeutralButtonStyle
    @style/ThemeOverlay_AppCompat_Dialog
    @attr/dialogTheme
    @dimen/abc_dialog_padding_material
    @attr/dialogPreferredPadding
    @dimen/abc_dialog_corner_radius_material
    @attr/dialogCornerRadius
    @style/ThemeOverlay_AppCompat_Dialog_Alert
    @attr/alertDialogTheme
    @style/AlertDialog_AppCompat_Light
    @attr/alertDialogStyle
    @attr/alertDialogCenterButtons
    @color/abc_primary_text_material_light
    @attr/textColorAlertDialogListItem
    @attr/listDividerAlertDialog
    @attr/windowFixedWidthMajor
    @attr/windowFixedWidthMinor
    @attr/windowFixedHeightMajor
    @attr/windowFixedHeightMinor
    @drawable/tooltip_frame_dark
    @attr/tooltipFrameBackground
    @color/foreground_material_dark
    @attr/tooltipForegroundColor
    @color/error_color_material_light
    @attr/colorError
@style/Base_V7_Theme_AppCompat_Light_Dialog : reachable=false
    @style/Base_Theme_AppCompat_Light
    @attr/colorBackgroundFloating
    @style/RtlOverlay_DialogWindowTitle_AppCompat
    @style/Base_DialogWindowTitleBackground_AppCompat
    @drawable/abc_dialog_material_background
    @style/Animation_AppCompat_Dialog
    @attr/windowActionBar
    @attr/windowActionModeOverlay
    @attr/listPreferredItemPaddingLeft
    @attr/listPreferredItemPaddingRight
    @style/Widget_AppCompat_ButtonBar_AlertDialog
    @style/Widget_AppCompat_Button_Borderless
@style/Base_V7_Widget_AppCompat_AutoCompleteTextView : reachable=false
    @attr/listChoiceBackgroundIndicator
    @drawable/abc_popup_background_mtrl_mult
    @attr/editTextBackground
    @attr/editTextColor
    @drawable/abc_text_cursor_material
@style/Base_V7_Widget_AppCompat_EditText : reachable=false
    @attr/editTextBackground
    @attr/editTextColor
    @drawable/abc_text_cursor_material
@style/Base_V7_Widget_AppCompat_Toolbar : reachable=false
    @style/TextAppearance_Widget_AppCompat_Toolbar_Title
    @attr/titleTextAppearance
    @style/TextAppearance_Widget_AppCompat_Toolbar_Subtitle
    @attr/subtitleTextAppearance
    @attr/actionBarSize
    @attr/titleMargin
    @dimen/abc_action_bar_default_height_material
    @attr/maxButtonHeight
    @attr/buttonGravity
    @attr/homeAsUpIndicator
    @attr/collapseIcon
    @string/abc_toolbar_collapse_description
    @attr/collapseContentDescription
    @attr/contentInsetStart
    @dimen/abc_action_bar_content_inset_with_nav
    @attr/contentInsetStartWithNavigation
    @dimen/abc_action_bar_default_padding_start_material
    @dimen/abc_action_bar_default_padding_end_material
@style/Base_Widget_AppCompat_ActionBar : reachable=false
    @attr/displayOptions
    @attr/dividerVertical
    @attr/divider
    @attr/actionBarSize
    @attr/height
    @style/TextAppearance_AppCompat_Widget_ActionBar_Title
    @attr/titleTextStyle
    @style/TextAppearance_AppCompat_Widget_ActionBar_Subtitle
    @attr/subtitleTextStyle
    @attr/background
    @attr/backgroundStacked
    @attr/backgroundSplit
    @style/Widget_AppCompat_ActionButton
    @attr/actionButtonStyle
    @style/Widget_AppCompat_ActionButton_Overflow
    @attr/actionOverflowButtonStyle
    @dimen/abc_action_bar_content_inset_material
    @attr/contentInsetStart
    @dimen/abc_action_bar_content_inset_with_nav
    @attr/contentInsetStartWithNavigation
    @attr/contentInsetEnd
    @dimen/abc_action_bar_elevation_material
    @attr/elevation
    @attr/actionBarPopupTheme
    @attr/popupTheme
@style/Base_Widget_AppCompat_ActionBar_Solid : reachable=false
    @style/Base_Widget_AppCompat_ActionBar
    @attr/colorPrimary
    @attr/background
    @attr/backgroundStacked
    @attr/backgroundSplit
@style/Base_Widget_AppCompat_ActionBar_TabBar : reachable=false
    @attr/actionBarDivider
    @attr/divider
    @attr/showDividers
    @attr/dividerPadding
@style/Base_Widget_AppCompat_ActionBar_TabText : reachable=false
@style/Base_Widget_AppCompat_ActionBar_TabView : reachable=false
@style/Base_Widget_AppCompat_ActionButton : reachable=false
@style/Base_Widget_AppCompat_ActionButton_CloseMode : reachable=false
@style/Base_Widget_AppCompat_ActionButton_Overflow : reachable=false
    @drawable/abc_ic_menu_overflow_material
    @attr/srcCompat
@style/Base_Widget_AppCompat_ActionMode : reachable=false
    @attr/actionModeBackground
    @attr/background
    @attr/actionModeSplitBackground
    @attr/backgroundSplit
    @attr/actionBarSize
    @attr/height
    @style/TextAppearance_AppCompat_Widget_ActionMode_Title
    @attr/titleTextStyle
    @style/TextAppearance_AppCompat_Widget_ActionMode_Subtitle
    @attr/subtitleTextStyle
    @layout/abc_action_mode_close_item_material
    @attr/closeItemLayout
    @attr/titleMargin
    @dimen/abc_action_bar_default_height_material
    @attr/maxButtonHeight
    @attr/buttonGravity
    @attr/contentInsetStart
    @dimen/abc_action_bar_content_inset_with_nav
    @attr/contentInsetStartWithNavigation
    @dimen/abc_action_bar_default_padding_start_material
    @dimen/abc_action_bar_default_padding_end_material
@style/Base_Widget_AppCompat_ActivityChooserView : reachable=false
    @drawable/abc_ab_share_pack_mtrl_alpha
    @attr/dividerVertical
    @attr/divider
    @attr/showDividers
    @attr/dividerPadding
@style/Base_Widget_AppCompat_AutoCompleteTextView : reachable=false
    @attr/editTextBackground
@style/Base_Widget_AppCompat_Button : reachable=false
@style/Base_Widget_AppCompat_ButtonBar : reachable=false
@style/Base_Widget_AppCompat_ButtonBar_AlertDialog : reachable=false
    @style/Base_Widget_AppCompat_ButtonBar
@style/Base_Widget_AppCompat_Button_Borderless : reachable=false
@style/Base_Widget_AppCompat_Button_Borderless_Colored : reachable=false
    @color/abc_btn_colored_borderless_text_material
@style/Base_Widget_AppCompat_Button_ButtonBar_AlertDialog : reachable=false
    @style/Widget_AppCompat_Button_Borderless_Colored
    @dimen/abc_alert_dialog_button_bar_height
@style/Base_Widget_AppCompat_Button_Colored : reachable=false
    @style/Base_Widget_AppCompat_Button
    @drawable/abc_btn_colored_material
    @style/TextAppearance_AppCompat_Widget_Button_Colored
@style/Base_Widget_AppCompat_Button_Small : reachable=false
@style/Base_Widget_AppCompat_CompoundButton_CheckBox : reachable=false
@style/Base_Widget_AppCompat_CompoundButton_RadioButton : reachable=false
@style/Base_Widget_AppCompat_CompoundButton_Switch : reachable=false
    @drawable/abc_switch_track_mtrl_alpha
    @attr/track
    @drawable/abc_switch_thumb_material
    @style/TextAppearance_AppCompat_Widget_Switch
    @attr/switchTextAppearance
    @attr/controlBackground
    @attr/showText
    @dimen/abc_switch_padding
    @attr/switchPadding
    @string/abc_capital_on
    @string/abc_capital_off
@style/Base_Widget_AppCompat_DrawerArrowToggle : reachable=false
    @style/Base_Widget_AppCompat_DrawerArrowToggle_Common
    @attr/barLength
    @attr/gapBetweenBars
    @attr/drawableSize
@style/Base_Widget_AppCompat_DrawerArrowToggle_Common : reachable=false
    @attr/color
    @attr/spinBars
    @attr/thickness
    @attr/arrowShaftLength
    @attr/arrowHeadLength
@style/Base_Widget_AppCompat_DropDownItem_Spinner : reachable=false
@style/Base_Widget_AppCompat_EditText : reachable=false
    @attr/editTextBackground
@style/Base_Widget_AppCompat_ImageButton : reachable=false
@style/Base_Widget_AppCompat_Light_ActionBar : reachable=false
    @style/Base_Widget_AppCompat_ActionBar
    @style/Widget_AppCompat_Light_ActionButton
    @attr/actionButtonStyle
    @style/Widget_AppCompat_Light_ActionButton_Overflow
    @attr/actionOverflowButtonStyle
@style/Base_Widget_AppCompat_Light_ActionBar_Solid : reachable=false
    @style/Base_Widget_AppCompat_Light_ActionBar
    @attr/colorPrimary
    @attr/background
    @attr/backgroundStacked
    @attr/backgroundSplit
@style/Base_Widget_AppCompat_Light_ActionBar_TabBar : reachable=false
    @style/Base_Widget_AppCompat_ActionBar_TabBar
@style/Base_Widget_AppCompat_Light_ActionBar_TabText : reachable=false
@style/Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse : reachable=false
@style/Base_Widget_AppCompat_Light_ActionBar_TabView : reachable=false
@style/Base_Widget_AppCompat_Light_PopupMenu : reachable=false
@style/Base_Widget_AppCompat_Light_PopupMenu_Overflow : reachable=false
    @style/Base_Widget_AppCompat_Light_PopupMenu
@style/Base_Widget_AppCompat_ListMenuView : reachable=false
    @drawable/abc_ic_arrow_drop_right_black_24dp
    @attr/subMenuArrow
@style/Base_Widget_AppCompat_ListPopupWindow : reachable=false
@style/Base_Widget_AppCompat_ListView : reachable=false
@style/Base_Widget_AppCompat_ListView_DropDown : reachable=false
@style/Base_Widget_AppCompat_ListView_Menu : reachable=false
    @style/Base_Widget_AppCompat_ListView
@style/Base_Widget_AppCompat_PopupMenu : reachable=false
@style/Base_Widget_AppCompat_PopupMenu_Overflow : reachable=false
    @style/Base_Widget_AppCompat_PopupMenu
@style/Base_Widget_AppCompat_PopupWindow : reachable=false
@style/Base_Widget_AppCompat_ProgressBar : reachable=false
@style/Base_Widget_AppCompat_ProgressBar_Horizontal : reachable=false
@style/Base_Widget_AppCompat_RatingBar : reachable=false
@style/Base_Widget_AppCompat_RatingBar_Indicator : reachable=false
    @drawable/abc_ratingbar_indicator_material
@style/Base_Widget_AppCompat_RatingBar_Small : reachable=false
    @drawable/abc_ratingbar_small_material
@style/Base_Widget_AppCompat_SearchView : reachable=false
    @layout/abc_search_view
    @attr/layout
    @drawable/abc_textfield_search_material
    @attr/queryBackground
    @attr/submitBackground
    @drawable/abc_ic_clear_material
    @attr/closeIcon
    @drawable/abc_ic_search_api_material
    @attr/searchIcon
    @attr/searchHintIcon
    @drawable/abc_ic_go_search_api_material
    @attr/goIcon
    @drawable/abc_ic_voice_search_api_material
    @attr/voiceIcon
    @drawable/abc_ic_commit_search_api_mtrl_alpha
    @attr/commitIcon
    @layout/abc_search_dropdown_item_icons_2line
    @attr/suggestionRowLayout
@style/Base_Widget_AppCompat_SearchView_ActionBar : reachable=false
    @style/Base_Widget_AppCompat_SearchView
    @attr/queryBackground
    @attr/submitBackground
    @attr/searchHintIcon
    @string/abc_search_hint
    @attr/defaultQueryHint
@style/Base_Widget_AppCompat_SeekBar : reachable=false
@style/Base_Widget_AppCompat_SeekBar_Discrete : reachable=false
    @style/Base_Widget_AppCompat_SeekBar
    @drawable/abc_seekbar_tick_mark_material
    @attr/tickMark
@style/Base_Widget_AppCompat_Spinner : reachable=false
@style/Base_Widget_AppCompat_Spinner_Underlined : reachable=false
    @style/Base_Widget_AppCompat_Spinner
    @drawable/abc_spinner_textfield_background_material
@style/Base_Widget_AppCompat_TextView : reachable=false
@style/Base_Widget_AppCompat_TextView_SpinnerItem : reachable=false
@style/Base_Widget_AppCompat_Toolbar : reachable=false
    @style/Base_V7_Widget_AppCompat_Toolbar
    @style/Base_V26_Widget_AppCompat_Toolbar
@style/Base_Widget_AppCompat_Toolbar_Button_Navigation : reachable=false
@style/LaunchTheme : reachable=true
    @drawable/launch_background
@style/NormalTheme : reachable=true
@style/Platform_AppCompat : reachable=false
    @style/Platform_V21_AppCompat
    @style/Platform_V25_AppCompat
@style/Platform_AppCompat_Light : reachable=false
    @style/Platform_V21_AppCompat_Light
    @style/Platform_V25_AppCompat_Light
@style/Platform_ThemeOverlay_AppCompat : reachable=false
    @attr/colorPrimary
    @attr/colorPrimaryDark
    @attr/colorAccent
    @attr/colorControlNormal
    @attr/colorControlActivated
    @attr/colorControlHighlight
    @attr/colorButtonNormal
@style/Platform_ThemeOverlay_AppCompat_Dark : reachable=false
    @style/Platform_ThemeOverlay_AppCompat
@style/Platform_ThemeOverlay_AppCompat_Light : reachable=false
    @style/Platform_ThemeOverlay_AppCompat
@style/Platform_V21_AppCompat : reachable=false
    @color/abc_hint_foreground_material_dark
    @color/abc_hint_foreground_material_light
    @attr/buttonBarStyle
    @attr/buttonBarButtonStyle
@style/Platform_V21_AppCompat_Light : reachable=false
    @color/abc_hint_foreground_material_light
    @color/abc_hint_foreground_material_dark
    @attr/buttonBarStyle
    @attr/buttonBarButtonStyle
@style/Platform_V25_AppCompat : reachable=false
@style/Platform_V25_AppCompat_Light : reachable=false
@style/Platform_Widget_AppCompat_Spinner : reachable=false
@style/RtlOverlay_DialogWindowTitle_AppCompat : reachable=false
    @style/Base_DialogWindowTitle_AppCompat
@style/RtlOverlay_Widget_AppCompat_ActionBar_TitleItem : reachable=false
@style/RtlOverlay_Widget_AppCompat_DialogTitle_Icon : reachable=false
@style/RtlOverlay_Widget_AppCompat_PopupMenuItem : reachable=false
@style/RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup : reachable=false
@style/RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut : reachable=false
@style/RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow : reachable=false
@style/RtlOverlay_Widget_AppCompat_PopupMenuItem_Text : reachable=false
@style/RtlOverlay_Widget_AppCompat_PopupMenuItem_Title : reachable=false
@style/RtlOverlay_Widget_AppCompat_SearchView_MagIcon : reachable=false
    @dimen/abc_dropdownitem_text_padding_left
@style/RtlOverlay_Widget_AppCompat_Search_DropDown : reachable=false
    @dimen/abc_dropdownitem_text_padding_left
@style/RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 : reachable=false
@style/RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 : reachable=false
@style/RtlOverlay_Widget_AppCompat_Search_DropDown_Query : reachable=false
@style/RtlOverlay_Widget_AppCompat_Search_DropDown_Text : reachable=false
    @style/Base_Widget_AppCompat_DropDownItem_Spinner
@style/RtlUnderlay_Widget_AppCompat_ActionButton : reachable=false
@style/RtlUnderlay_Widget_AppCompat_ActionButton_Overflow : reachable=false
    @style/Base_Widget_AppCompat_ActionButton
    @dimen/abc_action_bar_overflow_padding_start_material
    @dimen/abc_action_bar_overflow_padding_end_material
@style/TextAppearance_AppCompat : reachable=false
    @style/Base_TextAppearance_AppCompat
@style/TextAppearance_AppCompat_Body1 : reachable=false
    @style/Base_TextAppearance_AppCompat_Body1
@style/TextAppearance_AppCompat_Body2 : reachable=false
    @style/Base_TextAppearance_AppCompat_Body2
@style/TextAppearance_AppCompat_Button : reachable=false
    @style/Base_TextAppearance_AppCompat_Button
@style/TextAppearance_AppCompat_Caption : reachable=false
    @style/Base_TextAppearance_AppCompat_Caption
@style/TextAppearance_AppCompat_Display1 : reachable=false
    @style/Base_TextAppearance_AppCompat_Display1
@style/TextAppearance_AppCompat_Display2 : reachable=false
    @style/Base_TextAppearance_AppCompat_Display2
@style/TextAppearance_AppCompat_Display3 : reachable=false
    @style/Base_TextAppearance_AppCompat_Display3
@style/TextAppearance_AppCompat_Display4 : reachable=false
    @style/Base_TextAppearance_AppCompat_Display4
@style/TextAppearance_AppCompat_Headline : reachable=false
    @style/Base_TextAppearance_AppCompat_Headline
@style/TextAppearance_AppCompat_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Inverse
@style/TextAppearance_AppCompat_Large : reachable=false
    @style/Base_TextAppearance_AppCompat_Large
@style/TextAppearance_AppCompat_Large_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Large_Inverse
@style/TextAppearance_AppCompat_Light_SearchResult_Subtitle : reachable=false
    @style/TextAppearance_AppCompat_SearchResult_Subtitle
@style/TextAppearance_AppCompat_Light_SearchResult_Title : reachable=false
    @style/TextAppearance_AppCompat_SearchResult_Title
@style/TextAppearance_AppCompat_Light_Widget_PopupMenu_Large : reachable=false
    @style/TextAppearance_AppCompat_Widget_PopupMenu_Large
@style/TextAppearance_AppCompat_Light_Widget_PopupMenu_Small : reachable=false
    @style/TextAppearance_AppCompat_Widget_PopupMenu_Small
@style/TextAppearance_AppCompat_Medium : reachable=false
    @style/Base_TextAppearance_AppCompat_Medium
@style/TextAppearance_AppCompat_Medium_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Medium_Inverse
@style/TextAppearance_AppCompat_Menu : reachable=false
    @style/Base_TextAppearance_AppCompat_Menu
@style/TextAppearance_AppCompat_SearchResult_Subtitle : reachable=false
    @style/Base_TextAppearance_AppCompat_SearchResult_Subtitle
@style/TextAppearance_AppCompat_SearchResult_Title : reachable=false
    @style/Base_TextAppearance_AppCompat_SearchResult_Title
@style/TextAppearance_AppCompat_Small : reachable=false
    @style/Base_TextAppearance_AppCompat_Small
@style/TextAppearance_AppCompat_Small_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Small_Inverse
@style/TextAppearance_AppCompat_Subhead : reachable=false
    @style/Base_TextAppearance_AppCompat_Subhead
@style/TextAppearance_AppCompat_Subhead_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Subhead_Inverse
@style/TextAppearance_AppCompat_Title : reachable=false
    @style/Base_TextAppearance_AppCompat_Title
@style/TextAppearance_AppCompat_Title_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Title_Inverse
@style/TextAppearance_AppCompat_Tooltip : reachable=false
    @style/TextAppearance_AppCompat
@style/TextAppearance_AppCompat_Widget_ActionBar_Menu : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
@style/TextAppearance_AppCompat_Widget_ActionBar_Subtitle : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
@style/TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
@style/TextAppearance_AppCompat_Widget_ActionBar_Title : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_ActionBar_Title
@style/TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
@style/TextAppearance_AppCompat_Widget_ActionMode_Subtitle : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
@style/TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse : reachable=false
    @style/TextAppearance_AppCompat_Widget_ActionMode_Subtitle
@style/TextAppearance_AppCompat_Widget_ActionMode_Title : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_ActionMode_Title
@style/TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse : reachable=false
    @style/TextAppearance_AppCompat_Widget_ActionMode_Title
@style/TextAppearance_AppCompat_Widget_Button : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_Button
@style/TextAppearance_AppCompat_Widget_Button_Borderless_Colored : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
@style/TextAppearance_AppCompat_Widget_Button_Colored : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_Button_Colored
@style/TextAppearance_AppCompat_Widget_Button_Inverse : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_Button_Inverse
@style/TextAppearance_AppCompat_Widget_DropDownItem : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_DropDownItem
@style/TextAppearance_AppCompat_Widget_PopupMenu_Header : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
@style/TextAppearance_AppCompat_Widget_PopupMenu_Large : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
@style/TextAppearance_AppCompat_Widget_PopupMenu_Small : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
@style/TextAppearance_AppCompat_Widget_Switch : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_Switch
@style/TextAppearance_AppCompat_Widget_TextView_SpinnerItem : reachable=false
    @style/Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
@style/TextAppearance_Compat_Notification : reachable=false
@style/TextAppearance_Compat_Notification_Info : reachable=false
@style/TextAppearance_Compat_Notification_Line2 : reachable=false
    @style/TextAppearance_Compat_Notification_Info
@style/TextAppearance_Compat_Notification_Time : reachable=false
@style/TextAppearance_Compat_Notification_Title : reachable=false
@style/TextAppearance_Widget_AppCompat_ExpandedMenu_Item : reachable=false
    @style/Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
@style/TextAppearance_Widget_AppCompat_Toolbar_Subtitle : reachable=false
    @style/Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
@style/TextAppearance_Widget_AppCompat_Toolbar_Title : reachable=false
    @style/Base_TextAppearance_Widget_AppCompat_Toolbar_Title
@style/ThemeOverlay_AppCompat : reachable=false
    @style/Base_ThemeOverlay_AppCompat
@style/ThemeOverlay_AppCompat_ActionBar : reachable=false
    @style/Base_ThemeOverlay_AppCompat_ActionBar
@style/ThemeOverlay_AppCompat_Dark : reachable=false
    @style/Base_ThemeOverlay_AppCompat_Dark
@style/ThemeOverlay_AppCompat_Dark_ActionBar : reachable=false
    @style/Base_ThemeOverlay_AppCompat_Dark_ActionBar
@style/ThemeOverlay_AppCompat_DayNight : reachable=false
    @style/ThemeOverlay_AppCompat_Light
    @style/ThemeOverlay_AppCompat_Dark
@style/ThemeOverlay_AppCompat_DayNight_ActionBar : reachable=false
    @style/ThemeOverlay_AppCompat_DayNight
    @attr/colorControlNormal
    @style/Widget_AppCompat_SearchView_ActionBar
    @attr/searchViewStyle
@style/ThemeOverlay_AppCompat_Dialog : reachable=false
    @style/Base_ThemeOverlay_AppCompat_Dialog
@style/ThemeOverlay_AppCompat_Dialog_Alert : reachable=false
    @style/Base_ThemeOverlay_AppCompat_Dialog_Alert
@style/ThemeOverlay_AppCompat_Light : reachable=false
    @style/Base_ThemeOverlay_AppCompat_Light
@style/Theme_AppCompat : reachable=false
    @style/Base_Theme_AppCompat
@style/Theme_AppCompat_CompactMenu : reachable=true
    @style/Base_Theme_AppCompat_CompactMenu
@style/Theme_AppCompat_DayNight : reachable=false
    @style/Theme_AppCompat_Light
    @style/Theme_AppCompat
@style/Theme_AppCompat_DayNight_DarkActionBar : reachable=false
    @style/Theme_AppCompat_Light_DarkActionBar
    @style/Theme_AppCompat
@style/Theme_AppCompat_DayNight_Dialog : reachable=false
    @style/Theme_AppCompat_Light_Dialog
    @style/Theme_AppCompat_Dialog
@style/Theme_AppCompat_DayNight_DialogWhenLarge : reachable=false
    @style/Theme_AppCompat_Light_DialogWhenLarge
    @style/Theme_AppCompat_DialogWhenLarge
@style/Theme_AppCompat_DayNight_Dialog_Alert : reachable=false
    @style/Theme_AppCompat_Light_Dialog_Alert
    @style/Theme_AppCompat_Dialog_Alert
@style/Theme_AppCompat_DayNight_Dialog_MinWidth : reachable=false
    @style/Theme_AppCompat_Light_Dialog_MinWidth
    @style/Theme_AppCompat_Dialog_MinWidth
@style/Theme_AppCompat_DayNight_NoActionBar : reachable=false
    @style/Theme_AppCompat_Light_NoActionBar
    @style/Theme_AppCompat_NoActionBar
@style/Theme_AppCompat_Dialog : reachable=false
    @style/Base_Theme_AppCompat_Dialog
@style/Theme_AppCompat_DialogWhenLarge : reachable=false
    @style/Base_Theme_AppCompat_DialogWhenLarge
@style/Theme_AppCompat_Dialog_Alert : reachable=false
    @style/Base_Theme_AppCompat_Dialog_Alert
@style/Theme_AppCompat_Dialog_MinWidth : reachable=false
    @style/Base_Theme_AppCompat_Dialog_MinWidth
@style/Theme_AppCompat_Empty : reachable=true
@style/Theme_AppCompat_Light : reachable=true
    @style/Base_Theme_AppCompat_Light
@style/Theme_AppCompat_Light_DarkActionBar : reachable=false
    @style/Base_Theme_AppCompat_Light_DarkActionBar
@style/Theme_AppCompat_Light_Dialog : reachable=false
    @style/Base_Theme_AppCompat_Light_Dialog
@style/Theme_AppCompat_Light_DialogWhenLarge : reachable=false
    @style/Base_Theme_AppCompat_Light_DialogWhenLarge
@style/Theme_AppCompat_Light_Dialog_Alert : reachable=false
    @style/Base_Theme_AppCompat_Light_Dialog_Alert
@style/Theme_AppCompat_Light_Dialog_MinWidth : reachable=false
    @style/Base_Theme_AppCompat_Light_Dialog_MinWidth
@style/Theme_AppCompat_Light_NoActionBar : reachable=false
    @style/Theme_AppCompat_Light
    @attr/windowActionBar
    @attr/windowNoTitle
@style/Theme_AppCompat_NoActionBar : reachable=false
    @style/Theme_AppCompat
    @attr/windowActionBar
    @attr/windowNoTitle
@style/Widget_AppCompat_ActionBar : reachable=false
    @style/Base_Widget_AppCompat_ActionBar
@style/Widget_AppCompat_ActionBar_Solid : reachable=false
    @style/Base_Widget_AppCompat_ActionBar_Solid
@style/Widget_AppCompat_ActionBar_TabBar : reachable=false
    @style/Base_Widget_AppCompat_ActionBar_TabBar
@style/Widget_AppCompat_ActionBar_TabText : reachable=false
    @style/Base_Widget_AppCompat_ActionBar_TabText
@style/Widget_AppCompat_ActionBar_TabView : reachable=false
    @style/Base_Widget_AppCompat_ActionBar_TabView
@style/Widget_AppCompat_ActionButton : reachable=false
    @style/Base_Widget_AppCompat_ActionButton
@style/Widget_AppCompat_ActionButton_CloseMode : reachable=false
    @style/Base_Widget_AppCompat_ActionButton_CloseMode
@style/Widget_AppCompat_ActionButton_Overflow : reachable=false
    @style/Base_Widget_AppCompat_ActionButton_Overflow
@style/Widget_AppCompat_ActionMode : reachable=false
    @style/Base_Widget_AppCompat_ActionMode
@style/Widget_AppCompat_ActivityChooserView : reachable=false
    @style/Base_Widget_AppCompat_ActivityChooserView
@style/Widget_AppCompat_AutoCompleteTextView : reachable=false
    @style/Base_Widget_AppCompat_AutoCompleteTextView
@style/Widget_AppCompat_Button : reachable=false
    @style/Base_Widget_AppCompat_Button
@style/Widget_AppCompat_ButtonBar : reachable=false
    @style/Base_Widget_AppCompat_ButtonBar
@style/Widget_AppCompat_ButtonBar_AlertDialog : reachable=false
    @style/Base_Widget_AppCompat_ButtonBar_AlertDialog
@style/Widget_AppCompat_Button_Borderless : reachable=false
    @style/Base_Widget_AppCompat_Button_Borderless
@style/Widget_AppCompat_Button_Borderless_Colored : reachable=false
    @style/Base_Widget_AppCompat_Button_Borderless_Colored
@style/Widget_AppCompat_Button_ButtonBar_AlertDialog : reachable=false
    @style/Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
@style/Widget_AppCompat_Button_Colored : reachable=false
    @style/Base_Widget_AppCompat_Button_Colored
@style/Widget_AppCompat_Button_Small : reachable=false
    @style/Base_Widget_AppCompat_Button_Small
@style/Widget_AppCompat_CompoundButton_CheckBox : reachable=false
    @style/Base_Widget_AppCompat_CompoundButton_CheckBox
@style/Widget_AppCompat_CompoundButton_RadioButton : reachable=false
    @style/Base_Widget_AppCompat_CompoundButton_RadioButton
@style/Widget_AppCompat_CompoundButton_Switch : reachable=false
    @style/Base_Widget_AppCompat_CompoundButton_Switch
@style/Widget_AppCompat_DrawerArrowToggle : reachable=false
    @style/Base_Widget_AppCompat_DrawerArrowToggle
    @attr/colorControlNormal
    @attr/color
@style/Widget_AppCompat_DropDownItem_Spinner : reachable=false
    @style/RtlOverlay_Widget_AppCompat_Search_DropDown_Text
@style/Widget_AppCompat_EditText : reachable=false
    @style/Base_Widget_AppCompat_EditText
@style/Widget_AppCompat_ImageButton : reachable=false
    @style/Base_Widget_AppCompat_ImageButton
@style/Widget_AppCompat_Light_ActionBar : reachable=false
    @style/Base_Widget_AppCompat_Light_ActionBar
@style/Widget_AppCompat_Light_ActionBar_Solid : reachable=false
    @style/Base_Widget_AppCompat_Light_ActionBar_Solid
@style/Widget_AppCompat_Light_ActionBar_Solid_Inverse : reachable=false
    @style/Widget_AppCompat_Light_ActionBar_Solid
@style/Widget_AppCompat_Light_ActionBar_TabBar : reachable=false
    @style/Base_Widget_AppCompat_Light_ActionBar_TabBar
@style/Widget_AppCompat_Light_ActionBar_TabBar_Inverse : reachable=false
    @style/Widget_AppCompat_Light_ActionBar_TabBar
@style/Widget_AppCompat_Light_ActionBar_TabText : reachable=false
    @style/Base_Widget_AppCompat_Light_ActionBar_TabText
@style/Widget_AppCompat_Light_ActionBar_TabText_Inverse : reachable=false
    @style/Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
@style/Widget_AppCompat_Light_ActionBar_TabView : reachable=false
    @style/Base_Widget_AppCompat_Light_ActionBar_TabView
@style/Widget_AppCompat_Light_ActionBar_TabView_Inverse : reachable=false
    @style/Widget_AppCompat_Light_ActionBar_TabView
@style/Widget_AppCompat_Light_ActionButton : reachable=false
    @style/Widget_AppCompat_ActionButton
@style/Widget_AppCompat_Light_ActionButton_CloseMode : reachable=false
    @style/Widget_AppCompat_ActionButton_CloseMode
@style/Widget_AppCompat_Light_ActionButton_Overflow : reachable=false
    @style/Widget_AppCompat_ActionButton_Overflow
@style/Widget_AppCompat_Light_ActionMode_Inverse : reachable=false
    @style/Widget_AppCompat_ActionMode
@style/Widget_AppCompat_Light_ActivityChooserView : reachable=false
    @style/Widget_AppCompat_ActivityChooserView
@style/Widget_AppCompat_Light_AutoCompleteTextView : reachable=false
    @style/Widget_AppCompat_AutoCompleteTextView
@style/Widget_AppCompat_Light_DropDownItem_Spinner : reachable=false
    @style/Widget_AppCompat_DropDownItem_Spinner
@style/Widget_AppCompat_Light_ListPopupWindow : reachable=false
    @style/Widget_AppCompat_ListPopupWindow
@style/Widget_AppCompat_Light_ListView_DropDown : reachable=false
    @style/Widget_AppCompat_ListView_DropDown
@style/Widget_AppCompat_Light_PopupMenu : reachable=false
    @style/Base_Widget_AppCompat_Light_PopupMenu
@style/Widget_AppCompat_Light_PopupMenu_Overflow : reachable=false
    @style/Base_Widget_AppCompat_Light_PopupMenu_Overflow
@style/Widget_AppCompat_Light_SearchView : reachable=false
    @style/Widget_AppCompat_SearchView
@style/Widget_AppCompat_Light_Spinner_DropDown_ActionBar : reachable=false
    @style/Widget_AppCompat_Spinner_DropDown_ActionBar
@style/Widget_AppCompat_ListMenuView : reachable=false
    @style/Base_Widget_AppCompat_ListMenuView
@style/Widget_AppCompat_ListPopupWindow : reachable=false
    @style/Base_Widget_AppCompat_ListPopupWindow
@style/Widget_AppCompat_ListView : reachable=false
    @style/Base_Widget_AppCompat_ListView
@style/Widget_AppCompat_ListView_DropDown : reachable=false
    @style/Base_Widget_AppCompat_ListView_DropDown
@style/Widget_AppCompat_ListView_Menu : reachable=false
    @style/Base_Widget_AppCompat_ListView_Menu
@style/Widget_AppCompat_PopupMenu : reachable=false
    @style/Base_Widget_AppCompat_PopupMenu
@style/Widget_AppCompat_PopupMenu_Overflow : reachable=false
    @style/Base_Widget_AppCompat_PopupMenu_Overflow
@style/Widget_AppCompat_PopupWindow : reachable=false
    @style/Base_Widget_AppCompat_PopupWindow
@style/Widget_AppCompat_ProgressBar : reachable=false
    @style/Base_Widget_AppCompat_ProgressBar
@style/Widget_AppCompat_ProgressBar_Horizontal : reachable=false
    @style/Base_Widget_AppCompat_ProgressBar_Horizontal
@style/Widget_AppCompat_RatingBar : reachable=false
    @style/Base_Widget_AppCompat_RatingBar
@style/Widget_AppCompat_RatingBar_Indicator : reachable=false
    @style/Base_Widget_AppCompat_RatingBar_Indicator
@style/Widget_AppCompat_RatingBar_Small : reachable=false
    @style/Base_Widget_AppCompat_RatingBar_Small
@style/Widget_AppCompat_SearchView : reachable=false
    @style/Base_Widget_AppCompat_SearchView
@style/Widget_AppCompat_SearchView_ActionBar : reachable=false
    @style/Base_Widget_AppCompat_SearchView_ActionBar
@style/Widget_AppCompat_SeekBar : reachable=false
    @style/Base_Widget_AppCompat_SeekBar
@style/Widget_AppCompat_SeekBar_Discrete : reachable=false
    @style/Base_Widget_AppCompat_SeekBar_Discrete
@style/Widget_AppCompat_Spinner : reachable=false
    @style/Base_Widget_AppCompat_Spinner
@style/Widget_AppCompat_Spinner_DropDown : reachable=false
    @style/Widget_AppCompat_Spinner
@style/Widget_AppCompat_Spinner_DropDown_ActionBar : reachable=false
    @style/Widget_AppCompat_Spinner_DropDown
@style/Widget_AppCompat_Spinner_Underlined : reachable=false
    @style/Base_Widget_AppCompat_Spinner_Underlined
@style/Widget_AppCompat_TextView : reachable=false
    @style/Base_Widget_AppCompat_TextView
@style/Widget_AppCompat_TextView_SpinnerItem : reachable=false
    @style/Base_Widget_AppCompat_TextView_SpinnerItem
@style/Widget_AppCompat_Toolbar : reachable=false
    @style/Base_Widget_AppCompat_Toolbar
@style/Widget_AppCompat_Toolbar_Button_Navigation : reachable=false
    @style/Base_Widget_AppCompat_Toolbar_Button_Navigation
@style/Widget_Compat_NotificationActionContainer : reachable=false
    @drawable/notification_action_background
@style/Widget_Compat_NotificationActionText : reachable=false
    @color/androidx_core_secondary_text_default_material_light
    @dimen/notification_action_text_size

The root reachable resources are:
 anim:fragment_fast_out_extra_slow_in:2130771992
 animator:fragment_close_enter:2130837504
 animator:fragment_close_exit:2130837505
 animator:fragment_fade_enter:2130837506
 animator:fragment_fade_exit:2130837507
 animator:fragment_open_enter:2130837508
 animator:fragment_open_exit:2130837509
 attr:actionBarDivider:2130903040
 attr:actionBarItemBackground:2130903041
 attr:actionBarPopupTheme:2130903042
 attr:actionBarSize:2130903043
 attr:actionBarSplitStyle:2130903044
 attr:actionBarStyle:2130903045
 attr:actionBarTabBarStyle:2130903046
 attr:actionBarTabStyle:2130903047
 attr:actionBarTabTextStyle:2130903048
 attr:actionBarTheme:2130903049
 attr:actionBarWidgetTheme:2130903050
 attr:actionButtonStyle:2130903051
 attr:actionDropDownStyle:2130903052
 attr:actionLayout:2130903053
 attr:actionMenuTextAppearance:2130903054
 attr:actionMenuTextColor:2130903055
 attr:actionModeBackground:2130903056
 attr:actionModeCloseButtonStyle:2130903057
 attr:actionModeCloseContentDescription:2130903058
 attr:actionModeCloseDrawable:2130903059
 attr:actionModeCopyDrawable:**********
 attr:actionModeCutDrawable:**********
 attr:actionModeFindDrawable:**********
 attr:actionModePasteDrawable:**********
 attr:actionModePopupWindowStyle:**********
 attr:actionModeSelectAllDrawable:**********
 attr:actionModeShareDrawable:**********
 attr:actionModeSplitBackground:**********
 attr:actionModeStyle:**********
 attr:actionModeTheme:**********
 attr:actionModeWebSearchDrawable:**********
 attr:actionOverflowButtonStyle:**********
 attr:actionOverflowMenuStyle:**********
 attr:actionProviderClass:**********
 attr:actionViewClass:**********
 attr:activityAction:**********
 attr:activityChooserViewStyle:**********
 attr:activityName:**********
 attr:alertDialogCenterButtons:**********
 attr:alertDialogStyle:**********
 attr:alertDialogTheme:**********
 attr:alpha:**********
 attr:alphabeticModifiers:**********
 attr:autoCompleteTextViewStyle:**********
 attr:background:**********
 attr:backgroundSplit:**********
 attr:backgroundStacked:**********
 attr:backgroundTint:**********
 attr:backgroundTintMode:**********
 attr:buttonStyle:**********
 attr:checkboxStyle:**********
 attr:colorAccent:**********
 attr:colorButtonNormal:**********
 attr:colorControlActivated:**********
 attr:colorControlHighlight:**********
 attr:colorControlNormal:**********
 attr:colorSwitchThumbNormal:**********
 attr:contentDescription:**********
 attr:contentInsetEnd:**********
 attr:contentInsetEndWithActions:**********
 attr:contentInsetLeft:**********
 attr:contentInsetRight:**********
 attr:contentInsetStart:2130903138
 attr:contentInsetStartWithNavigation:2130903139
 attr:dialogTheme:**********
 attr:displayOptions:**********
 attr:drawableBottomCompat:**********
 attr:drawableEndCompat:**********
 attr:drawableLeftCompat:**********
 attr:drawableRightCompat:**********
 attr:drawableSize:**********
 attr:drawableStartCompat:**********
 attr:drawableTint:**********
 attr:drawableTintMode:**********
 attr:drawableTopCompat:**********
 attr:dropDownListViewStyle:**********
 attr:editTextStyle:**********
 attr:font:**********
 attr:fontFamily:**********
 attr:fontProviderAuthority:**********
 attr:fontProviderCerts:**********
 attr:fontProviderFetchStrategy:**********
 attr:fontProviderFetchTimeout:**********
 attr:fontProviderPackage:**********
 attr:fontProviderQuery:**********
 attr:fontProviderSystemFontFamily:**********
 attr:fontStyle:**********
 attr:fontVariationSettings:**********
 attr:fontWeight:**********
 attr:height:**********
 attr:imageButtonStyle:**********
 attr:itemPadding:**********
 attr:lineHeight:**********
 attr:listChoiceBackgroundIndicator:**********
 attr:listChoiceIndicatorMultipleAnimated:**********
 attr:listChoiceIndicatorSingleAnimated:**********
 attr:listDividerAlertDialog:**********
 attr:listItemLayout:**********
 attr:listLayout:**********
 attr:listMenuViewStyle:**********
 attr:listPopupWindowStyle:**********
 attr:listPreferredItemHeight:**********
 attr:listPreferredItemHeightLarge:**********
 attr:listPreferredItemHeightSmall:**********
 attr:listPreferredItemPaddingEnd:**********
 attr:listPreferredItemPaddingLeft:**********
 attr:listPreferredItemPaddingRight:**********
 attr:listPreferredItemPaddingStart:**********
 attr:menu:**********
 attr:nestedScrollViewStyle:**********
 attr:panelMenuListTheme:**********
 attr:primaryActivityName:2130903240
 attr:progressBarPadding:2130903241
 attr:progressBarStyle:2130903242
 attr:queryBackground:2130903243
 attr:queryHint:2130903244
 attr:queryPatterns:2130903245
 attr:radioButtonStyle:2130903246
 attr:ratingBarStyle:2130903247
 attr:searchViewStyle:2130903252
 attr:secondaryActivityAction:2130903253
 attr:secondaryActivityName:2130903254
 attr:seekBarStyle:2130903255
 attr:shortcutMatchRequired:2130903258
 attr:spinnerStyle:2130903266
 attr:state_above_anchor:2130903273
 attr:textAllCaps:2130903285
 attr:textAppearanceLargePopupMenu:2130903286
 attr:textAppearanceListItem:2130903287
 attr:textAppearanceListItemSecondary:2130903288
 attr:textAppearanceListItemSmall:2130903289
 attr:textAppearancePopupMenuHeader:2130903290
 attr:textAppearanceSearchResultSubtitle:2130903291
 attr:textAppearanceSearchResultTitle:2130903292
 attr:textAppearanceSmallPopupMenu:2130903293
 attr:textColorAlertDialogListItem:2130903294
 attr:textColorSearchUrl:2130903295
 attr:textLocale:2130903296
 attr:tint:2130903305
 attr:tintMode:2130903306
 attr:toolbarNavigationButtonStyle:2130903317
 attr:toolbarStyle:2130903318
 attr:tooltipForegroundColor:2130903319
 attr:tooltipFrameBackground:2130903320
 attr:tooltipText:2130903321
 attr:viewInflaterClass:2130903326
 attr:windowActionBar:2130903328
 attr:windowActionBarOverlay:2130903329
 attr:windowActionModeOverlay:2130903330
 attr:windowFixedHeightMajor:2130903331
 attr:windowFixedHeightMinor:2130903332
 attr:windowFixedWidthMajor:2130903333
 attr:windowFixedWidthMinor:2130903334
 attr:windowMinWidthMajor:2130903335
 attr:windowMinWidthMinor:2130903336
 attr:windowNoTitle:2130903337
 bool:abc_action_bar_embed_tabs:2130968576
 color:abc_decor_view_status_guard:2131034117
 color:abc_decor_view_status_guard_light:2131034118
 color:abc_tint_btn_checkable:2131034131
 color:abc_tint_default:2131034132
 color:abc_tint_edittext:2131034133
 color:abc_tint_seek_thumb:2131034134
 color:abc_tint_spinner:2131034135
 color:abc_tint_switch_track:2131034136
 color:accent_material_dark:2131034137
 color:accent_material_light:2131034138
 color:androidx_core_ripple_material_light:2131034139
 color:androidx_core_secondary_text_default_material_light:2131034140
 color:background_floating_material_dark:2131034141
 color:background_floating_material_light:2131034142
 color:background_material_dark:2131034143
 color:background_material_light:2131034144
 color:error_color_material_dark:2131034157
 color:error_color_material_light:2131034158
 color:notification_action_color_filter:2131034175
 color:notification_icon_bg_color:2131034176
 color:tooltip_background_dark:2131034197
 color:tooltip_background_light:2131034198
 dimen:abc_action_bar_stacked_max_height:2131099657
 dimen:abc_action_bar_stacked_tab_max_width:2131099658
 dimen:abc_cascading_menus_min_smallest_width:2131099670
 dimen:abc_config_prefDialogWidth:2131099671
 dimen:abc_dropdownitem_icon_width:2131099689
 dimen:abc_dropdownitem_text_padding_left:2131099690
 dimen:abc_search_view_preferred_height:2131099702
 dimen:abc_search_view_preferred_width:2131099703
 dimen:abc_star_big:2131099707
 dimen:abc_star_medium:2131099708
 dimen:abc_star_small:2131099709
 dimen:notification_action_icon_size:2131099745
 dimen:notification_action_text_size:2131099746
 dimen:notification_big_circle_margin:2131099747
 dimen:notification_content_margin_start:2131099748
 dimen:notification_large_icon_height:2131099749
 dimen:notification_large_icon_width:2131099750
 dimen:notification_main_column_padding_top:2131099751
 dimen:notification_media_narrow_margin:2131099752
 dimen:notification_right_icon_size:2131099753
 dimen:notification_right_side_padding_top:2131099754
 dimen:notification_small_icon_background_padding:2131099755
 dimen:notification_small_icon_size_as_large:2131099756
 dimen:notification_subtext_size:2131099757
 dimen:notification_top_pad:2131099758
 dimen:notification_top_pad_large_text:2131099759
 dimen:tooltip_corner_radius:2131099760
 dimen:tooltip_horizontal_padding:2131099761
 dimen:tooltip_margin:2131099762
 dimen:tooltip_precise_anchor_extra_offset:2131099763
 dimen:tooltip_precise_anchor_threshold:2131099764
 dimen:tooltip_vertical_padding:2131099765
 dimen:tooltip_y_offset_non_touch:2131099766
 dimen:tooltip_y_offset_touch:2131099767
 drawable:abc_ab_share_pack_mtrl_alpha:2131165184
 drawable:abc_btn_borderless_material:2131165186
 drawable:abc_btn_check_material:2131165187
 drawable:abc_btn_check_material_anim:2131165188
 drawable:abc_btn_colored_material:2131165191
 drawable:abc_btn_default_mtrl_shape:2131165192
 drawable:abc_btn_radio_material:2131165193
 drawable:abc_btn_radio_material_anim:2131165194
 drawable:abc_cab_background_internal_bg:2131165199
 drawable:abc_cab_background_top_material:2131165200
 drawable:abc_cab_background_top_mtrl_alpha:2131165201
 drawable:abc_dialog_material_background:2131165203
 drawable:abc_edit_text_material:2131165204
 drawable:abc_ic_ab_back_material:2131165205
 drawable:abc_ic_commit_search_api_mtrl_alpha:2131165208
 drawable:abc_ic_menu_copy_mtrl_am_alpha:2131165210
 drawable:abc_ic_menu_cut_mtrl_alpha:2131165211
 drawable:abc_ic_menu_paste_mtrl_am_alpha:2131165213
 drawable:abc_ic_menu_selectall_mtrl_alpha:2131165214
 drawable:abc_ic_menu_share_mtrl_alpha:2131165215
 drawable:abc_list_divider_mtrl_alpha:2131165221
 drawable:abc_menu_hardkey_panel_mtrl_mult:2131165232
 drawable:abc_popup_background_mtrl_mult:2131165233
 drawable:abc_ratingbar_indicator_material:2131165234
 drawable:abc_ratingbar_material:2131165235
 drawable:abc_ratingbar_small_material:2131165236
 drawable:abc_seekbar_thumb_material:2131165242
 drawable:abc_seekbar_tick_mark_material:2131165243
 drawable:abc_seekbar_track_material:2131165244
 drawable:abc_spinner_mtrl_am_alpha:2131165245
 drawable:abc_spinner_textfield_background_material:2131165246
 drawable:abc_star_black_48dp:2131165247
 drawable:abc_star_half_black_48dp:2131165248
 drawable:abc_switch_thumb_material:2131165249
 drawable:abc_switch_track_mtrl_alpha:2131165250
 drawable:abc_tab_indicator_material:2131165251
 drawable:abc_text_cursor_material:2131165253
 drawable:abc_text_select_handle_left_mtrl:2131165254
 drawable:abc_text_select_handle_middle_mtrl:2131165255
 drawable:abc_text_select_handle_right_mtrl:2131165256
 drawable:abc_textfield_activated_mtrl_alpha:2131165257
 drawable:abc_textfield_default_mtrl_alpha:2131165258
 drawable:abc_textfield_search_activated_mtrl_alpha:2131165259
 drawable:abc_textfield_search_default_mtrl_alpha:2131165260
 drawable:abc_textfield_search_material:2131165261
 drawable:abc_vector_test:2131165262
 drawable:notification_action_background:2131165272
 drawable:notification_bg:2131165273
 drawable:notification_bg_low:2131165274
 drawable:notification_bg_low_normal:2131165275
 drawable:notification_bg_low_pressed:2131165276
 drawable:notification_bg_normal:2131165277
 drawable:notification_bg_normal_pressed:2131165278
 drawable:notification_icon_background:2131165279
 drawable:notification_template_icon_bg:2131165280
 drawable:notification_template_icon_low_bg:2131165281
 drawable:notification_tile_bg:2131165282
 drawable:notify_panel_notification_icon_bg:2131165283
 drawable:tooltip_frame_dark:**********
 drawable:tooltip_frame_light:**********
 id:accessibility_action_clickable_span:2131230726
 id:accessibility_custom_action_0:2131230727
 id:accessibility_custom_action_1:2131230728
 id:accessibility_custom_action_10:2131230729
 id:accessibility_custom_action_11:2131230730
 id:accessibility_custom_action_12:2131230731
 id:accessibility_custom_action_13:2131230732
 id:accessibility_custom_action_14:2131230733
 id:accessibility_custom_action_15:2131230734
 id:accessibility_custom_action_16:2131230735
 id:accessibility_custom_action_17:2131230736
 id:accessibility_custom_action_18:2131230737
 id:accessibility_custom_action_19:2131230738
 id:accessibility_custom_action_2:2131230739
 id:accessibility_custom_action_20:2131230740
 id:accessibility_custom_action_21:2131230741
 id:accessibility_custom_action_22:2131230742
 id:accessibility_custom_action_23:2131230743
 id:accessibility_custom_action_24:2131230744
 id:accessibility_custom_action_25:2131230745
 id:accessibility_custom_action_26:2131230746
 id:accessibility_custom_action_27:2131230747
 id:accessibility_custom_action_28:2131230748
 id:accessibility_custom_action_29:2131230749
 id:accessibility_custom_action_3:2131230750
 id:accessibility_custom_action_30:2131230751
 id:accessibility_custom_action_31:2131230752
 id:accessibility_custom_action_4:2131230753
 id:accessibility_custom_action_5:2131230754
 id:accessibility_custom_action_6:2131230755
 id:accessibility_custom_action_7:2131230756
 id:accessibility_custom_action_8:2131230757
 id:accessibility_custom_action_9:2131230758
 id:action_bar:**********
 id:action_bar_activity_content:**********
 id:action_bar_container:**********
 id:action_bar_root:**********
 id:action_bar_spinner:**********
 id:action_bar_subtitle:**********
 id:action_bar_title:**********
 id:action_container:**********
 id:action_context_bar:**********
 id:action_divider:2131230768
 id:action_image:2131230769
 id:action_menu_divider:2131230770
 id:action_menu_presenter:2131230771
 id:action_mode_bar:2131230772
 id:action_mode_bar_stub:2131230773
 id:action_mode_close_button:2131230774
 id:action_text:2131230775
 id:actions:2131230776
 id:activity_chooser_view_content:2131230777
 id:alertTitle:2131230779
 id:androidx_window_activity_scope:2131230781
 id:buttonPanel:2131230786
 id:content:**********
 id:contentPanel:2131230793
 id:custom:2131230794
 id:customPanel:2131230795
 id:decor_content_parent:2131230796
 id:edit_query:2131230800
 id:fragment_container_view_tag:2131230805
 id:group_divider:2131230806
 id:image:**********
 id:info:2131230813
 id:italic:2131230814
 id:line1:2131230815
 id:line3:2131230816
 id:listMode:2131230817
 id:list_item:2131230818
 id:locale:2131230819
 id:message:2131230821
 id:none:2131230825
 id:notification_background:2131230827
 id:notification_main_column:2131230828
 id:notification_main_column_container:2131230829
 id:off:2131230830
 id:on:2131230831
 id:parentPanel:2131230832
 id:progress_circular:2131230833
 id:progress_horizontal:2131230834
 id:scrollIndicatorDown:2131230840
 id:scrollIndicatorUp:2131230841
 id:scrollView:2131230842
 id:search_button:2131230845
 id:search_close_btn:2131230846
 id:search_edit_frame:2131230847
 id:search_go_btn:2131230848
 id:search_mag_icon:2131230849
 id:search_plate:2131230850
 id:search_src_text:2131230851
 id:search_voice_btn:2131230852
 id:shortcut:2131230854
 id:spacer:2131230858
 id:special_effects_controller_view_tag:2131230859
 id:split_action_bar:2131230860
 id:submenuarrow:2131230864
 id:submit_area:2131230865
 id:tag_accessibility_actions:2131230867
 id:tag_accessibility_clickable_spans:2131230868
 id:tag_accessibility_heading:2131230869
 id:tag_accessibility_pane_title:2131230870
 id:tag_on_apply_window_listener:2131230871
 id:tag_on_receive_content_listener:2131230872
 id:tag_on_receive_content_mime_types:2131230873
 id:tag_screen_reader_focusable:2131230874
 id:tag_state_description:2131230875
 id:tag_unhandled_key_event_manager:2131230877
 id:tag_unhandled_key_listeners:2131230878
 id:tag_window_insets_animation_callback:2131230879
 id:text:2131230880
 id:text2:2131230881
 id:textSpacerNoButtons:2131230882
 id:textSpacerNoTitle:2131230883
 id:title:2131230885
 id:titleDividerNoCustom:2131230886
 id:title_template:2131230887
 id:top:2131230888
 id:topPanel:2131230889
 id:view_tree_lifecycle_owner:2131230894
 id:view_tree_saved_state_registry_owner:2131230895
 id:view_tree_view_model_store_owner:2131230896
 id:visible_removing_fragment_view_tag:2131230897
 integer:cancel_button_image_alpha:2131296258
 layout:abc_action_bar_title_item:2131427328
 layout:abc_action_menu_item_layout:2131427330
 layout:abc_action_menu_layout:2131427331
 layout:abc_action_mode_close_item_material:2131427333
 layout:abc_cascading_menu_item_layout:2131427339
 layout:abc_dialog_title_material:2131427340
 layout:abc_expanded_menu_layout:2131427341
 layout:abc_list_menu_item_checkbox:2131427342
 layout:abc_list_menu_item_icon:2131427343
 layout:abc_list_menu_item_layout:2131427344
 layout:abc_list_menu_item_radio:2131427345
 layout:abc_popup_menu_header_item_layout:2131427346
 layout:abc_popup_menu_item_layout:2131427347
 layout:abc_screen_simple:2131427349
 layout:abc_screen_simple_overlay_action_mode:2131427350
 layout:abc_screen_toolbar:2131427351
 layout:abc_search_dropdown_item_icons_2line:2131427352
 layout:abc_search_view:2131427353
 layout:abc_tooltip:2131427355
 layout:notification_action:2131427357
 layout:notification_action_tombstone:2131427358
 layout:notification_template_custom_big:2131427359
 layout:notification_template_icon_group:2131427360
 layout:notification_template_part_chronometer:2131427361
 layout:notification_template_part_time:2131427362
 layout:support_simple_spinner_dropdown_item:2131427366
 mipmap:ic_launcher:2131492864
 string:abc_action_bar_up_description:2131558401
 string:abc_menu_alt_shortcut_label:2131558408
 string:abc_menu_ctrl_shortcut_label:2131558409
 string:abc_menu_delete_shortcut_label:2131558410
 string:abc_menu_enter_shortcut_label:2131558411
 string:abc_menu_function_shortcut_label:2131558412
 string:abc_menu_meta_shortcut_label:2131558413
 string:abc_menu_shift_shortcut_label:2131558414
 string:abc_menu_space_shortcut_label:2131558415
 string:abc_menu_sym_shortcut_label:2131558416
 string:abc_prepend_shortcut_label:2131558417
 string:abc_searchview_description_search:2131558421
 style:Animation_AppCompat_Tooltip:2131623940
 style:LaunchTheme:2131624097
 style:NormalTheme:2131624098
 style:Theme_AppCompat_CompactMenu:2131624183
 style:Theme_AppCompat_Empty:2131624195
 style:Theme_AppCompat_Light:2131624196
Skipped unused resource res/anim/abc_fade_in.xml: 549 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/anim/abc_fade_out.xml: 549 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/anim/abc_slide_in_bottom.xml: 555 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/anim/abc_slide_in_top.xml: 556 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/anim/abc_slide_out_bottom.xml: 555 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/anim/abc_slide_out_top.xml: 556 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color/abc_background_cache_hint_selector_material_dark.xml: 471 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color/abc_background_cache_hint_selector_material_light.xml: 473 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color/abc_btn_colored_text_material.xml: 676 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color/abc_primary_text_disable_only_material_dark.xml: 525 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color/abc_primary_text_disable_only_material_light.xml: 529 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color/abc_primary_text_material_dark.xml: 521 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color/abc_secondary_text_material_dark.xml: 529 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color/abc_secondary_text_material_light.xml: 533 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color/switch_thumb_material_dark.xml: 519 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/color-v23/abc_btn_colored_text_material.xml: 624 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/drawable/abc_item_background_holo_dark.xml: 2036 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/drawable/abc_list_selector_background_transition_holo_dark.xml: 422 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/drawable/abc_list_selector_holo_dark.xml: 2125 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/drawable-mdpi-v4/abc_list_pressed_holo_dark.9.png: 207 bytes (replaced with small dummy file of size 77 bytes)
Skipped unused resource res/drawable-mdpi-v4/abc_list_selector_disabled_holo_dark.9.png: 217 bytes (replaced with small dummy file of size 77 bytes)
Skipped unused resource res/drawable-hdpi-v4/abc_list_pressed_holo_dark.9.png: 208 bytes (replaced with small dummy file of size 77 bytes)
Skipped unused resource res/drawable-hdpi-v4/abc_list_selector_disabled_holo_dark.9.png: 228 bytes (replaced with small dummy file of size 77 bytes)
Skipped unused resource res/drawable-xhdpi-v4/abc_list_pressed_holo_dark.9.png: 209 bytes (replaced with small dummy file of size 77 bytes)
Skipped unused resource res/drawable-xhdpi-v4/abc_list_selector_disabled_holo_dark.9.png: 236 bytes (replaced with small dummy file of size 77 bytes)
Skipped unused resource res/drawable-xxhdpi-v4/abc_list_pressed_holo_dark.9.png: 212 bytes (replaced with small dummy file of size 77 bytes)
Skipped unused resource res/drawable-xxhdpi-v4/abc_list_selector_disabled_holo_dark.9.png: 260 bytes (replaced with small dummy file of size 77 bytes)
Skipped unused resource res/layout/abc_action_bar_up_container.xml: 588 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/layout/abc_activity_chooser_view.xml: 3884 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/layout/abc_activity_chooser_view_list_item.xml: 2684 bytes (replaced with small dummy file of size 9 bytes)
Skipped unused resource res/layout/custom_dialog.xml: 828 bytes (replaced with small dummy file of size 9 bytes)
