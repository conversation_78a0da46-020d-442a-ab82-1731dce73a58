<libraries>
  <library
      name="__local_aars__:D:\UM980 APP\gnss_controller_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
      jars="D:\UM980 APP\gnss_controller_app\build\app\intermediates\flutter\release\libs.jar"
      resolved="__local_aars__:D:\UM980 APP\gnss_controller_app\build\app\intermediates\flutter\release\libs.jar:unspecified"/>
  <library
      name="D:\UM980 APP\gnss_controller_app\android@@:flutter_blue_plus_android::release"
      jars="D:\UM980 APP\gnss_controller_app\build\flutter_blue_plus_android\.transforms\ed5eb0022f8c43d17c591ced77b8d8ba\transformed\out\jars\classes.jar;D:\UM980 APP\gnss_controller_app\build\flutter_blue_plus_android\.transforms\ed5eb0022f8c43d17c591ced77b8d8ba\transformed\out\jars\libs\R.jar"
      resolved="com.lib.flutter_blue_plus:flutter_blue_plus_android:unspecified"
      folder="D:\UM980 APP\gnss_controller_app\build\flutter_blue_plus_android\.transforms\ed5eb0022f8c43d17c591ced77b8d8ba\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\UM980 APP\gnss_controller_app\android@@:flutter_bluetooth_serial::release"
      jars="D:\UM980 APP\gnss_controller_app\build\flutter_bluetooth_serial\.transforms\c7f6386f7cc47e14c2f24af493ac56d0\transformed\out\jars\classes.jar;D:\UM980 APP\gnss_controller_app\build\flutter_bluetooth_serial\.transforms\c7f6386f7cc47e14c2f24af493ac56d0\transformed\out\jars\libs\R.jar"
      resolved="io.github.edufolly.flutterbluetoothserial:flutter_bluetooth_serial:unspecified"
      folder="D:\UM980 APP\gnss_controller_app\build\flutter_bluetooth_serial\.transforms\c7f6386f7cc47e14c2f24af493ac56d0\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\UM980 APP\gnss_controller_app\android@@:path_provider_android::release"
      jars="D:\UM980 APP\gnss_controller_app\build\path_provider_android\.transforms\94d43ba3e40e07e168e1babce17614cc\transformed\out\jars\classes.jar;D:\UM980 APP\gnss_controller_app\build\path_provider_android\.transforms\94d43ba3e40e07e168e1babce17614cc\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.pathprovider:path_provider_android:unspecified"
      folder="D:\UM980 APP\gnss_controller_app\build\path_provider_android\.transforms\94d43ba3e40e07e168e1babce17614cc\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\UM980 APP\gnss_controller_app\android@@:permission_handler_android::release"
      jars="D:\UM980 APP\gnss_controller_app\build\permission_handler_android\.transforms\057bb8afe32b876d52326011d30a6ce3\transformed\out\jars\classes.jar;D:\UM980 APP\gnss_controller_app\build\permission_handler_android\.transforms\057bb8afe32b876d52326011d30a6ce3\transformed\out\jars\libs\R.jar"
      resolved="com.baseflow.permissionhandler:permission_handler_android:unspecified"
      folder="D:\UM980 APP\gnss_controller_app\build\permission_handler_android\.transforms\057bb8afe32b876d52326011d30a6ce3\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\UM980 APP\gnss_controller_app\android@@:shared_preferences_android::release"
      jars="D:\UM980 APP\gnss_controller_app\build\shared_preferences_android\.transforms\f60d597401d8d9783ad02e27261312d1\transformed\out\jars\classes.jar;D:\UM980 APP\gnss_controller_app\build\shared_preferences_android\.transforms\f60d597401d8d9783ad02e27261312d1\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.sharedpreferences:shared_preferences_android:unspecified"
      folder="D:\UM980 APP\gnss_controller_app\build\shared_preferences_android\.transforms\f60d597401d8d9783ad02e27261312d1\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\UM980 APP\gnss_controller_app\android@@:webview_flutter_android::release"
      jars="D:\UM980 APP\gnss_controller_app\build\webview_flutter_android\.transforms\ffdcbad0587ef9f508e7873da7a4e469\transformed\out\jars\classes.jar;D:\UM980 APP\gnss_controller_app\build\webview_flutter_android\.transforms\ffdcbad0587ef9f508e7873da7a4e469\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.webviewflutter:webview_flutter_android:unspecified"
      folder="D:\UM980 APP\gnss_controller_app\build\webview_flutter_android\.transforms\ffdcbad0587ef9f508e7873da7a4e469\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\flutter_embedding_release\1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d\fc6a0eb79ea6f9f5b76545ede277bbbdab10b125\flutter_embedding_release-1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d"/>
  <library
      name="androidx.window:window-java:1.0.0-beta04@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\2a215381204751055df81b6269e717b7\transformed\jetified-window-java-1.0.0-beta04\jars\classes.jar"
      resolved="androidx.window:window-java:1.0.0-beta04"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\2a215381204751055df81b6269e717b7\transformed\jetified-window-java-1.0.0-beta04"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.0.0-beta04@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f302e75fb7efd781bcf1a29281c188da\transformed\jetified-window-1.0.0-beta04\jars\classes.jar"
      resolved="androidx.window:window:1.0.0-beta04"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f302e75fb7efd781bcf1a29281c188da\transformed\jetified-window-1.0.0-beta04"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.5.2\d246a704a55b7bddb79407cce4348890eaa341d9\kotlinx-coroutines-android-1.5.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.5.2\f4cc07a50437659e0043e7da762809a46932b6a0\kotlinx-coroutines-core-jvm-1.5.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.7.10\d70d7d2c56371f7aa18f32e984e3e2e998fe9081\kotlin-stdlib-jdk8-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.10"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\armeabi_v7a_release\1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d\5e9d03663f812d2967104ca5b66007dfce1f4f73\armeabi_v7a_release-1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\arm64_v8a_release\1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d\88195c35b8a2cb676a249b24b356f1f51d6cf047\arm64_v8a_release-1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\x86_64_release\1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d\76c3da2f27bbf2950241475aaf6a45b085b0337a\x86_64_release-1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d.jar"
      resolved="io.flutter:x86_64_release:1.0.0-55eae6864b296dd9f43b2cc7577ec256e5c32a8d"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.2.0\cd3478503da69b1a7e0319bd2d1389943db9b364\lifecycle-common-java8-2.2.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.2.0"/>
  <library
      name="androidx.fragment:fragment:1.3.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\2a01b1183e75fe4b0614d118dad4ff32\transformed\fragment-1.3.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.3.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\2a01b1183e75fe4b0614d118dad4ff32\transformed\fragment-1.3.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\eeb3b3d28863e46e8405d58b4e4ce9b4\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\eeb3b3d28863e46e8405d58b4e4ce9b4\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\3dd24de0bfb0a58b7b0437cba93312a0\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\3dd24de0bfb0a58b7b0437cba93312a0\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.2.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6b1c607cf54ff69c6cb873491d035f0a\transformed\jetified-activity-1.2.3\jars\classes.jar"
      resolved="androidx.activity:activity:1.2.3"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6b1c607cf54ff69c6cb873491d035f0a\transformed\jetified-activity-1.2.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e7089abc1d97d688192104f9e7c95213\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e7089abc1d97d688192104f9e7c95213\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\284a410c767ad6c7a2b0ffc8d8d85981\transformed\core-1.6.0\jars\classes.jar"
      resolved="androidx.core:core:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\284a410c767ad6c7a2b0ffc8d8d85981\transformed\core-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\82112359ef27c86626e392f5169dc84a\transformed\lifecycle-runtime-2.3.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\82112359ef27c86626e392f5169dc84a\transformed\lifecycle-runtime-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c73563d3d7d1646950aa3601306362a3\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c73563d3d7d1646950aa3601306362a3\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\d128b5c60f72b4cfbf02fb59bd328cf2\transformed\lifecycle-livedata-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\d128b5c60f72b4cfbf02fb59bd328cf2\transformed\lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ac50be9cb799a54587ffbd9871921f4a\transformed\lifecycle-livedata-core-2.3.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ac50be9cb799a54587ffbd9871921f4a\transformed\lifecycle-livedata-core-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.3.1\fc466261d52f4433863642fb40d12441ae274a98\lifecycle-common-2.3.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.3.1"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\2cc54ca65f294fdb232731188da6beeb\transformed\core-runtime-2.1.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\2cc54ca65f294fdb232731188da6beeb\transformed\core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.1.0\b3152fc64428c9354344bd89848ecddc09b6f07e\core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\17b52f3ce5be01faa0d2efabbb4c4451\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\17b52f3ce5be01faa0d2efabbb4c4451\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8ceccbad81f8154151300ad77a454b5e\transformed\lifecycle-viewmodel-2.3.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8ceccbad81f8154151300ad77a454b5e\transformed\lifecycle-viewmodel-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\03e2e1f9f69ec4e16149dea78e7b1cde\transformed\jetified-savedstate-1.1.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\03e2e1f9f69ec4e16149dea78e7b1cde\transformed\jetified-savedstate-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.2\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.2.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.7.10\1ef73fee66f45d52c67e2aca12fd945dbe0659bf\kotlin-stdlib-jdk7-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.7.10\d2abf9e77736acc4450dc4a3f707fa2c10f5099d\kotlin-stdlib-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.7.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.7.10\bac80c520d0a9e3f3673bc2658c6ed02ef45a76a\kotlin-stdlib-common-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\3d2f03fc996c2e0aa3dee5cfa3002cd1\transformed\jetified-annotation-experimental-1.1.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\3d2f03fc996c2e0aa3dee5cfa3002cd1\transformed\jetified-annotation-experimental-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\bb8521d17d051bfde5328e053af79289\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\bb8521d17d051bfde5328e053af79289\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e75666861c19ddc5e1b02c228a732c3d\transformed\jetified-datastore-preferences-1.0.0\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e75666861c19ddc5e1b02c228a732c3d\transformed\jetified-datastore-preferences-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ea5100a471779cdfb4c61328eadd49f2\transformed\jetified-datastore-1.0.0\jars\classes.jar"
      resolved="androidx.datastore:datastore:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ea5100a471779cdfb4c61328eadd49f2\transformed\jetified-datastore-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-core:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core\1.0.0\403f64499b9a8994f5f7010329ddd1ee5c919ed5\datastore-preferences-core-1.0.0.jar"
      resolved="androidx.datastore:datastore-preferences-core:1.0.0"/>
  <library
      name="androidx.datastore:datastore-core:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-core\1.0.0\91b04fb657294e2906d95dce6a9e5a851f6125c1\datastore-core-1.0.0.jar"
      resolved="androidx.datastore:datastore-core:1.0.0"/>
  <library
      name="androidx.appcompat:appcompat:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c78ef3d8dbf2252ab7789223422cf8f0\transformed\appcompat-1.3.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c78ef3d8dbf2252ab7789223422cf8f0\transformed\appcompat-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9a629a05922c9885ca2407a9e8df7a44\transformed\webkit-1.12.0\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9a629a05922c9885ca2407a9e8df7a44\transformed\webkit-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6e2f84880daa8d4e3c4ce401aad10d8d\transformed\jetified-appcompat-resources-1.3.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6e2f84880daa8d4e3c4ce401aad10d8d\transformed\jetified-appcompat-resources-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6dee81f4b4549bc48be005f8afac87fb\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6dee81f4b4549bc48be005f8afac87fb\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\66e541bee9a2e5824ce955dd449fe267\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\66e541bee9a2e5824ce955dd449fe267\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\004a652fa9f75364a4c49b10d48e5180\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\004a652fa9f75364a4c49b10d48e5180\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9664ed077fa9fcb5dd93b34b7b882b37\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9664ed077fa9fcb5dd93b34b7b882b37\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b9c5a11634d8068a0d3a82731f07a468\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b9c5a11634d8068a0d3a82731f07a468\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
