<lint-module
    format="1"
    dir="D:\UM980 APP\gnss_controller_app\android\app"
    name=":app"
    type="APP"
    maven="android:app:"
    gradle="7.3.0"
    buildFolder="D:\UM980 APP\gnss_controller_app\build\app"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\30.0.3\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
