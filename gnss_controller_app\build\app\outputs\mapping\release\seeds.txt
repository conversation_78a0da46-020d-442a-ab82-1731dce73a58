androidx.window.embedding.SplitController
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface
androidx.window.embedding.ActivityStack
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface
androidx.appcompat.widget.ActionBarContextView
androidx.window.embedding.SplitRule$LayoutDir
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter
androidx.window.layout.WindowInfoTracker
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1$invokeSuspend$$inlined$collect$1
androidx.window.layout.ExtensionInterfaceCompat
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
androidx.core.app.CoreComponentFactory
androidx.window.layout.ExtensionsWindowLayoutInfoAdapter
androidx.appcompat.widget.ActionBarOverlayLayout
androidx.core.widget.NestedScrollView
androidx.window.layout.FoldingFeature$State
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper
androidx.lifecycle.SavedStateHandleController
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface
androidx.window.embedding.SplitRuleParser
androidx.window.layout.HardwareFoldingFeature$Type
androidx.appcompat.widget.ContentFrameLayout
androidx.window.layout.WindowMetricsCalculator$Companion$reset$1
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants
androidx.window.layout.SidecarWindowBackend$Companion
androidx.window.layout.ActivityCompatHelperApi24
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface
androidx.lifecycle.Lifecycling$1
androidx.window.layout.FoldingFeature$OcclusionType$Companion
kotlin.internal.jdk8.JDK8PlatformImplementations
androidx.window.embedding.ExtensionEmbeddingBackend$Companion
androidx.window.embedding.EmbeddingRule
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
androidx.window.embedding.EmbeddingInterfaceCompat$EmbeddingCallbackInterface
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface
androidx.appcompat.widget.Toolbar
androidx.window.layout.DisplayCompatHelperApi17
io.flutter.embedding.engine.FlutterOverlaySurface
androidx.window.R$styleable
androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface
androidx.appcompat.view.menu.ExpandedMenuView
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface
androidx.window.embedding.SplitController$Companion
androidx.appcompat.view.menu.ActionMenuItemView
androidx.window.layout.FoldingFeature$Orientation$Companion
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface
androidx.appcompat.widget.SearchView
androidx.window.layout.SidecarWindowBackend$ExtensionListenerImpl
androidx.window.embedding.EmbeddingInterfaceCompat
androidx.appcompat.widget.DialogTitle
org.chromium.support_lib_boundary.StaticsBoundaryInterface
androidx.lifecycle.CompositeGeneratedAdaptersObserver
androidx.window.core.Version
com.example.gnss_controller_app.MainActivity
androidx.window.layout.FoldingFeature$OcclusionType
androidx.appcompat.widget.SearchView$SearchAutoComplete
androidx.window.R$attr
androidx.lifecycle.LiveData$LifecycleBoundObserver
androidx.window.layout.DisplayCompatHelperApi28
androidx.window.java.R
androidx.window.layout.HardwareFoldingFeature$Companion
androidx.lifecycle.FullLifecycleObserverAdapter
androidx.window.layout.FoldingFeature
androidx.window.R
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface
androidx.savedstate.SavedStateRegistry$1
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
androidx.webkit.WebViewClientCompat
androidx.window.layout.WindowBackend
androidx.window.embedding.EmptyEmbeddingComponent
androidx.lifecycle.ReflectiveGenericLifecycleObserver
androidx.window.layout.SidecarCompat$TranslatingCallback
androidx.window.layout.SidecarCompat$DistinctElementCallback
androidx.window.embedding.EmbeddingTranslatingCallback
io.flutter.embedding.engine.FlutterJNI
androidx.window.layout.WindowInfoTrackerDecorator
androidx.window.layout.DisplayFeature
io.flutter.view.TextureRegistry$SurfaceTextureEntry
androidx.window.layout.SidecarCompat
androidx.activity.ComponentActivity$5
androidx.appcompat.widget.FitWindowsFrameLayout
androidx.window.layout.HardwareFoldingFeature$Type$Companion
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport
androidx.window.layout.WindowMetricsCalculatorCompat
androidx.window.layout.EmptyDecorator
androidx.window.layout.SidecarCompat$registerConfigurationChangeListener$configChangeObserver$1
androidx.appcompat.widget.ViewStubCompat
androidx.window.embedding.SplitRule
kotlinx.coroutines.internal.MainDispatcherFactory
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface
androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.window.core.Bounds
androidx.lifecycle.SingleGeneratedAdapterObserver
androidx.window.embedding.ExtensionEmbeddingBackend$EmbeddingCallbackImpl
android.support.v4.app.RemoteActionCompatParcelizer
androidx.appcompat.widget.ActionBarContainer
androidx.window.core.Version$bigInteger$2
io.flutter.view.TextureRegistry$ImageTextureEntry
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface
androidx.window.layout.WindowMetricsCalculator
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface
io.flutter.plugins.GeneratedPluginRegistrant
org.chromium.support_lib_boundary.WebAuthnCallbackBoundaryInterface
kotlin.coroutines.jvm.internal.BaseContinuationImpl
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants$ProcessGlobalConfigMapKey
androidx.window.layout.ExtensionWindowLayoutInfoBackend
io.flutter.view.FlutterCallbackInformation
androidx.window.embedding.EmbeddingCompat
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
androidx.window.core.ExperimentalWindowApi
androidx.window.embedding.EmbeddingCompat$Companion
io.flutter.plugin.platform.SingleViewPresentation
kotlinx.coroutines.android.AndroidDispatcherFactory
io.flutter.view.TextureRegistry$ImageConsumer
androidx.window.layout.WindowInfoTrackerImpl$Companion
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface
androidx.activity.ComponentActivity$3
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface
androidx.window.layout.WindowLayoutInfo
kotlinx.coroutines.android.AndroidExceptionPreHandler
androidx.activity.result.ActivityResultRegistry$1
androidx.appcompat.widget.ButtonBarLayout
androidx.savedstate.Recreator
androidx.activity.ImmLeaksCleaner
androidx.versionedparcelable.ParcelImpl
kotlinx.coroutines.internal.StackTraceRecoveryKt
androidx.window.layout.ActivityCompatHelperApi30
kotlin.internal.jdk7.JDK7PlatformImplementations
androidx.window.R$id
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior
androidx.annotation.Keep
androidx.window.layout.FoldingFeature$Orientation
androidx.fragment.app.FragmentManager$6
io.flutter.view.AccessibilityViewEmbedder
androidx.window.layout.SidecarAdapter
androidx.appcompat.widget.AlertDialogLayout
androidx.appcompat.widget.FitWindowsLinearLayout
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer
androidx.window.layout.WindowMetricsCalculator$Companion$decorator$1
androidx.window.embedding.ActivityFilter
androidx.appcompat.widget.ActionMenuView
androidx.window.layout.SidecarAdapter$Companion
androidx.window.layout.WindowMetricsCalculator$Companion
io.flutter.view.TextureRegistry$SurfaceProducer
androidx.lifecycle.SavedStateHandleController$1
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface
androidx.window.embedding.SplitPairFilter
androidx.window.embedding.MatcherUtils
androidx.core.graphics.drawable.IconCompat
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior
org.chromium.support_lib_boundary.ProfileBoundaryInterface
androidx.window.layout.WindowMetrics
androidx.core.app.RemoteActionCompat
androidx.window.embedding.ActivityRule
androidx.versionedparcelable.CustomVersionedParcelable
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus
androidx.window.core.Version$Companion
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
androidx.window.embedding.SplitInfo
androidx.window.layout.SidecarCompat$Companion
androidx.window.embedding.SplitPlaceholderRule
org.chromium.support_lib_boundary.WebMessageBoundaryInterface
androidx.window.layout.SidecarWindowBackend
androidx.window.embedding.EmbeddingAdapter
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface
androidx.window.layout.HardwareFoldingFeature
androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper
androidx.appcompat.view.menu.ListMenuItemView
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface
androidx.window.embedding.ExtensionEmbeddingBackend
androidx.window.layout.SidecarCompat$FirstAttachAdapter
androidx.window.layout.WindowInfoTracker$Companion
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface
androidx.window.embedding.SplitPairRule
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1
androidx.window.layout.FoldingFeature$State$Companion
androidx.window.layout.WindowMetricsCalculator$Companion$overrideDecorator$1
androidx.window.embedding.SplitRule$Api30Impl
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface
androidx.window.layout.WindowMetricsCalculatorDecorator
kotlinx.coroutines.CoroutineExceptionHandler
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
androidx.core.graphics.drawable.IconCompatParcelizer
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
androidx.core.app.RemoteActionCompatParcelizer
androidx.fragment.app.Fragment$5
androidx.appcompat.app.AlertController$RecycleListView
androidx.window.layout.WindowInfoTrackerImpl
androidx.appcompat.widget.ActivityChooserView$InnerLayout
androidx.activity.ComponentActivity$4
androidx.window.embedding.EmbeddingBackend
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface
androidx.activity.OnBackPressedDispatcher$LifecycleOnBackPressedCancellable
io.flutter.view.TextureRegistry$GLTextureConsumer
androidx.window.embedding.SplitPlaceholderRule: java.util.Set filters
androidx.window.layout.HardwareFoldingFeature$Type: androidx.window.layout.HardwareFoldingFeature$Type$Companion Companion
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
kotlin.jvm.internal.CallableReference: java.lang.Class owner
androidx.window.R$styleable: int SplitPairFilter_primaryActivityName
androidx.window.layout.SidecarCompat$TranslatingCallback: androidx.window.layout.SidecarCompat this$0
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage lastDequeuedImage
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
kotlin.jvm.internal.CallableReference: kotlin.reflect.KCallable reflected
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: java.util.concurrent.locks.ReentrantLock multicastConsumerLock
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int APP
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int DISABLED
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
androidx.window.embedding.SplitPairFilter: java.lang.String secondaryActivityIntentAction
androidx.fragment.app.FragmentState: android.os.Parcelable$Creator CREATOR
androidx.window.R$id: int ltr
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
kotlin.jvm.internal.FunctionReference: int flags
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
androidx.window.layout.WindowInfoTracker$Companion: boolean DEBUG
androidx.window.layout.SidecarCompat$DistinctElementCallback: java.util.WeakHashMap activityWindowLayoutInfo
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: int label
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback: androidx.window.layout.SidecarAdapter sidecarAdapter
androidx.window.layout.EmptyDecorator: androidx.window.layout.EmptyDecorator INSTANCE
androidx.window.layout.SidecarWindowBackend$ExtensionListenerImpl: androidx.window.layout.SidecarWindowBackend this$0
androidx.window.layout.SidecarWindowBackend: androidx.window.layout.SidecarWindowBackend$Companion Companion
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED
androidx.window.core.Version: java.lang.String VERSION_PATTERN_STRING
androidx.window.R$styleable: int[] ActivityFilter
androidx.fragment.app.FragmentManagerState: android.os.Parcelable$Creator CREATOR
androidx.window.layout.WindowInfoTrackerImpl: androidx.window.layout.WindowBackend windowBackend
androidx.window.embedding.SplitController: boolean sDebug
androidx.window.R$attr: int secondaryActivityName
androidx.window.core.Version: kotlin.Lazy bigInteger$delegate
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
androidx.window.R$attr: int alwaysExpand
androidx.window.core.Version: java.lang.String description
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
androidx.window.layout.HardwareFoldingFeature$Type: java.lang.String description
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_APP_TRIGGER
androidx.window.embedding.MatcherUtils: java.lang.String sMatchersTag
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
androidx.window.R$styleable: int SplitPlaceholderRule_splitLayoutDirection
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: androidx.window.layout.WindowLayoutInfo lastKnownValue
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter: java.util.Map consumerToJobMap
androidx.window.layout.SidecarCompat$registerConfigurationChangeListener$configChangeObserver$1: androidx.window.layout.SidecarCompat this$0
io.flutter.embedding.engine.FlutterJNI: float displayDensity
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
androidx.window.layout.WindowMetrics: androidx.window.core.Bounds _bounds
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: float displayWidth
androidx.window.layout.HardwareFoldingFeature: androidx.window.layout.FoldingFeature$State state
androidx.window.R$styleable: int SplitPlaceholderRule_placeholderActivityName
kotlin.coroutines.jvm.internal.BaseContinuationImpl: kotlin.coroutines.Continuation completion
androidx.window.layout.ActivityCompatHelperApi30: androidx.window.layout.ActivityCompatHelperApi30 INSTANCE
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
androidx.window.core.Version: androidx.window.core.Version VERSION_1_0
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: androidx.core.util.Consumer callback
androidx.window.layout.WindowMetricsCalculator$Companion$reset$1: androidx.window.layout.WindowMetricsCalculator$Companion$reset$1 INSTANCE
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1: androidx.core.util.Consumer $consumer
androidx.window.layout.ExtensionWindowLayoutInfoBackend: java.util.concurrent.locks.ReentrantLock extensionWindowBackendLock
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
androidx.window.embedding.ExtensionEmbeddingBackend: java.util.concurrent.CopyOnWriteArrayList splitChangeCallbacks
kotlinx.coroutines.channels.ArrayChannel: int size
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
androidx.window.layout.WindowInfoTrackerImpl: androidx.window.layout.WindowInfoTrackerImpl$Companion Companion
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int MEDIA_QUERY_ONLY
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
kotlin.coroutines.jvm.internal.SuspendLambda: int arity
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
kotlin.jvm.internal.Lambda: int arity
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: android.app.Activity activity
androidx.window.layout.ExtensionWindowLayoutInfoBackend: androidx.window.extensions.layout.WindowLayoutComponent component
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
kotlinx.coroutines.JobSupport: java.lang.Object _state
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
androidx.window.R$attr: int placeholderActivityName
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
androidx.window.R$styleable: int SplitPlaceholderRule_splitMinWidth
androidx.window.embedding.ExtensionEmbeddingBackend: androidx.window.embedding.EmbeddingInterfaceCompat embeddingExtension
androidx.window.core.Bounds: int left
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
androidx.window.embedding.SplitRule: int minSmallestWidth
androidx.window.layout.SidecarCompat$FirstAttachAdapter: androidx.window.layout.SidecarCompat sidecarCompat
androidx.window.layout.WindowMetricsCalculator$Companion$decorator$1: androidx.window.layout.WindowMetricsCalculator$Companion$decorator$1 INSTANCE
androidx.window.embedding.SplitRule: int layoutDirection
kotlinx.coroutines.InvokeOnCancelling: int _invoked
androidx.window.layout.ExtensionWindowLayoutInfoBackend: java.util.Map listenerToActivity
androidx.window.embedding.ActivityStack: java.util.List activities
androidx.window.layout.FoldingFeature$OcclusionType: androidx.window.layout.FoldingFeature$OcclusionType NONE
androidx.window.layout.FoldingFeature$OcclusionType: androidx.window.layout.FoldingFeature$OcclusionType$Companion Companion
androidx.window.R$attr: int primaryActivityName
androidx.window.layout.SidecarCompat: androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface extensionCallback
androidx.window.embedding.ActivityRule: boolean alwaysExpand
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: java.util.concurrent.Executor executor
androidx.window.embedding.EmbeddingCompat: boolean DEBUG
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: java.util.Set registeredListeners
androidx.window.embedding.SplitPairFilter: android.content.ComponentName secondaryActivityName
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
androidx.window.embedding.ExtensionEmbeddingBackend: java.util.concurrent.CopyOnWriteArraySet splitRules
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: androidx.window.layout.WindowInfoTrackerImpl this$0
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
androidx.window.R$styleable: int SplitPairRule_splitMinWidth
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
androidx.window.R$styleable: int SplitPairFilter_secondaryActivityName
kotlin.coroutines.AbstractCoroutineContextElement: kotlin.coroutines.CoroutineContext$Key key
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback: androidx.window.sidecar.SidecarDeviceState lastDeviceState
androidx.window.layout.FoldingFeature$State: androidx.window.layout.FoldingFeature$State FLAT
androidx.window.embedding.ExtensionEmbeddingBackend: java.util.concurrent.locks.ReentrantLock globalLock
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
androidx.window.R$styleable: int SplitPlaceholderRule_splitRatio
androidx.window.layout.DisplayCompatHelperApi17: androidx.window.layout.DisplayCompatHelperApi17 INSTANCE
kotlinx.coroutines.scheduling.LimitingDispatcher: int inFlightTasks
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
androidx.window.R$styleable: int[] ActivityRule
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int BROWSER
androidx.window.layout.SidecarWindowBackend: java.lang.String TAG
androidx.window.R$attr: int clearTop
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int NONE
androidx.window.R$styleable: int[] SplitPairRule
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int DISABLED
androidx.datastore.preferences.PreferencesProto$Value: int bitField0_
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1: kotlinx.coroutines.flow.Flow $flow
androidx.window.R$id: int rtl
io.flutter.embedding.engine.FlutterOverlaySurface: int id
androidx.window.embedding.ActivityFilter: java.lang.String intentAction
androidx.window.R$id: int locale
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
androidx.window.layout.FoldingFeature$OcclusionType: androidx.window.layout.FoldingFeature$OcclusionType FULL
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: androidx.window.layout.WindowLayoutInfo lastInfo
androidx.window.embedding.SplitPlaceholderRule: android.content.Intent placeholderIntent
androidx.window.core.Version$bigInteger$2: androidx.window.core.Version this$0
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
androidx.window.R$styleable: int SplitPairRule_splitRatio
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
androidx.window.R$attr: int finishSecondaryWithPrimary
androidx.window.layout.WindowLayoutInfo: java.util.List displayFeatures
androidx.window.embedding.SplitController: androidx.window.embedding.SplitController globalInstance
androidx.window.R$styleable: int ActivityRule_alwaysExpand
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
androidx.window.embedding.ActivityFilter: android.content.ComponentName componentName
kotlin.jvm.internal.CallableReference: boolean isTopLevel
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
androidx.window.core.Version: int major
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int WEB_SOURCE_AND_WEB_TRIGGER
androidx.window.layout.ActivityCompatHelperApi24: androidx.window.layout.ActivityCompatHelperApi24 INSTANCE
androidx.window.R$attr: int splitRatio
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
androidx.window.embedding.EmbeddingCompat: androidx.window.extensions.embedding.ActivityEmbeddingComponent embeddingExtension
androidx.window.embedding.SplitInfo: androidx.window.embedding.ActivityStack secondaryActivityStack
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
androidx.window.layout.FoldingFeature$Orientation: androidx.window.layout.FoldingFeature$Orientation HORIZONTAL
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
kotlinx.coroutines.CancellableContinuationImpl: int _decision
androidx.window.R$id: int androidx_window_activity_scope
androidx.appcompat.widget.AppCompatSpinner$SavedState: android.os.Parcelable$Creator CREATOR
androidx.window.layout.FoldingFeature$State: androidx.window.layout.FoldingFeature$State HALF_OPENED
kotlin.jvm.internal.CallableReference: java.lang.Object NO_RECEIVER
androidx.window.embedding.SplitPairRule: boolean finishSecondaryWithPrimary
androidx.window.layout.SidecarCompat$registerConfigurationChangeListener$configChangeObserver$1: android.app.Activity $activity
androidx.window.embedding.MatcherUtils: boolean sDebugMatchers
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
androidx.window.layout.SidecarWindowBackend: boolean DEBUG
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: java.lang.Object L$0
androidx.window.embedding.EmbeddingCompat: androidx.window.embedding.EmbeddingAdapter adapter
androidx.window.layout.HardwareFoldingFeature$Type: androidx.window.layout.HardwareFoldingFeature$Type FOLD
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_BASE_PATH
androidx.window.embedding.ExtensionEmbeddingBackend: androidx.window.embedding.ExtensionEmbeddingBackend$Companion Companion
androidx.window.layout.SidecarCompat: java.lang.String TAG
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
androidx.window.embedding.SplitController: java.util.Set staticSplitRules
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int PREFER_MEDIA_QUERY_OVER_FORCE_DARK
androidx.window.layout.HardwareFoldingFeature: androidx.window.layout.HardwareFoldingFeature$Companion Companion
androidx.window.embedding.EmbeddingCompat: java.lang.String TAG
androidx.window.embedding.SplitController: androidx.window.embedding.SplitController$Companion Companion
androidx.window.embedding.SplitInfo: androidx.window.embedding.ActivityStack primaryActivityStack
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
androidx.window.R$styleable: int SplitPlaceholderRule_splitMinSmallestWidth
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
androidx.window.layout.WindowInfoTrackerImpl: int BUFFER_CAPACITY
androidx.window.embedding.SplitRule: int minWidth
androidx.window.embedding.ExtensionEmbeddingBackend: java.lang.String TAG
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
androidx.activity.result.ActivityResult: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
androidx.window.R$styleable: int SplitPairRule_splitLayoutDirection
androidx.window.layout.FoldingFeature$Orientation: java.lang.String description
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
androidx.window.embedding.EmbeddingTranslatingCallback: androidx.window.embedding.EmbeddingAdapter adapter
androidx.window.embedding.ActivityStack: boolean isEmpty
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_STRING
androidx.window.layout.WindowMetricsCalculator$Companion: kotlin.jvm.functions.Function1 decorator
androidx.window.layout.ExtensionsWindowLayoutInfoAdapter: androidx.window.layout.ExtensionsWindowLayoutInfoAdapter INSTANCE
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
androidx.window.layout.SidecarWindowBackend: androidx.window.layout.SidecarWindowBackend globalInstance
androidx.window.embedding.SplitRule$Api30Impl: androidx.window.embedding.SplitRule$Api30Impl INSTANCE
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: android.app.Activity $activity
androidx.window.layout.SidecarWindowBackend: java.util.concurrent.CopyOnWriteArrayList windowLayoutChangeCallbacks
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
kotlinx.coroutines.channels.AbstractSendChannel: java.lang.Object onCloseHandler
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
androidx.window.embedding.ExtensionEmbeddingBackend$EmbeddingCallbackImpl: java.util.List lastInfo
androidx.window.embedding.ExtensionEmbeddingBackend: androidx.window.embedding.ExtensionEmbeddingBackend globalInstance
kotlin.coroutines.jvm.internal.ContinuationImpl: kotlin.coroutines.CoroutineContext _context
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
androidx.window.layout.WindowInfoTracker: androidx.window.layout.WindowInfoTracker$Companion Companion
androidx.window.embedding.SplitInfo: float splitRatio
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_ARRAY_BUFFER
androidx.fragment.app.BackStackState: android.os.Parcelable$Creator CREATOR
androidx.window.R$styleable: int SplitPairRule_finishSecondaryWithPrimary
androidx.window.embedding.ExtensionEmbeddingBackend$EmbeddingCallbackImpl: androidx.window.embedding.ExtensionEmbeddingBackend this$0
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
androidx.window.layout.SidecarCompat: androidx.window.sidecar.SidecarInterface sidecar
androidx.window.layout.WindowMetricsCalculatorCompat: java.lang.String TAG
androidx.window.R$styleable: int SplitPairRule_splitMinSmallestWidth
androidx.window.layout.HardwareFoldingFeature$Type: androidx.window.layout.HardwareFoldingFeature$Type HINGE
androidx.window.layout.SidecarCompat: androidx.window.layout.SidecarAdapter sidecarAdapter
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1: int label
androidx.window.embedding.ActivityRule: java.util.Set filters
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback: androidx.window.sidecar.SidecarInterface$SidecarCallback callbackInterface
androidx.window.embedding.ExtensionEmbeddingBackend: androidx.window.embedding.ExtensionEmbeddingBackend$EmbeddingCallbackImpl splitInfoEmbeddingCallback
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: java.lang.Object L$1
androidx.window.layout.WindowMetricsCalculator$Companion: androidx.window.layout.WindowMetricsCalculator$Companion $$INSTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
androidx.window.embedding.SplitPairRule: boolean clearTop
androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper: androidx.core.util.Consumer callback
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
kotlin.jvm.internal.CallableReference: java.lang.String name
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
androidx.window.embedding.SplitPairRule: java.util.Set filters
androidx.window.layout.HardwareFoldingFeature: androidx.window.core.Bounds featureBounds
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
androidx.window.layout.SidecarCompat: androidx.window.layout.SidecarCompat$Companion Companion
kotlinx.coroutines.CompletedExceptionally: int _handled
androidx.window.layout.FoldingFeature$Orientation: androidx.window.layout.FoldingFeature$Orientation VERTICAL
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper: android.app.Activity activity
androidx.window.R$attr: int activityName
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int FORCE_DARK_ONLY
androidx.window.embedding.EmbeddingCompat: androidx.window.embedding.EmbeddingCompat$Companion Companion
kotlin.jvm.internal.CallableReference: java.lang.String signature
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
androidx.window.R$styleable: int SplitPairFilter_secondaryActivityAction
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1$invokeSuspend$$inlined$collect$1: androidx.core.util.Consumer $consumer$inlined
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
androidx.window.layout.SidecarCompat: java.util.Map windowListenerRegisteredContexts
androidx.window.layout.SidecarCompat: java.util.Map componentCallbackMap
androidx.window.layout.SidecarCompat$FirstAttachAdapter: java.lang.ref.WeakReference activityWeakReference
kotlinx.coroutines.sync.MutexImpl: java.lang.Object _state
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
androidx.window.core.Version: androidx.window.core.Version UNKNOWN
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
androidx.window.R$styleable: int SplitPairRule_clearTop
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: java.lang.Object L$2
kotlinx.coroutines.DispatchedCoroutine: int _decision
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
androidx.window.R$styleable: int SplitPairRule_finishPrimaryWithSecondary
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
androidx.window.core.Version: androidx.window.core.Version VERSION_0_1
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
androidx.window.core.Bounds: int right
kotlinx.coroutines.CommonPool: java.util.concurrent.Executor pool
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
androidx.window.layout.SidecarCompat$DistinctElementCallback: java.util.concurrent.locks.ReentrantLock lock
androidx.window.layout.SidecarWindowBackend: androidx.window.layout.ExtensionInterfaceCompat windowExtension
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
androidx.window.layout.WindowInfoTracker$Companion: androidx.window.layout.WindowInfoTracker$Companion $$INSTANCE
androidx.datastore.preferences.protobuf.GeneratedMessageLite$ExtendableMessage: androidx.datastore.preferences.protobuf.FieldSet extensions
io.flutter.embedding.engine.FlutterJNI: float displayHeight
androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper: java.util.concurrent.Executor executor
androidx.window.embedding.EmbeddingTranslatingCallback: androidx.window.embedding.EmbeddingInterfaceCompat$EmbeddingCallbackInterface callback
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
androidx.window.layout.FoldingFeature$OcclusionType: java.lang.String description
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED_WITHOUT_APP_IDENTITY
androidx.window.layout.SidecarWindowBackend: java.util.concurrent.locks.ReentrantLock globalLock
androidx.window.layout.SidecarAdapter: java.lang.String TAG
androidx.window.layout.WindowInfoTracker$Companion: androidx.window.layout.WindowInfoTrackerDecorator decorator
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_SUFFIX
androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback: java.util.WeakHashMap mActivityWindowLayoutInfo
kotlinx.coroutines.DefaultExecutor: int debugStatus
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_WEB_TRIGGER
kotlin.coroutines.jvm.internal.ContinuationImpl: kotlin.coroutines.Continuation intercepted
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
androidx.window.R$attr: int splitMinSmallestWidth
androidx.window.R$styleable: int ActivityFilter_activityName
androidx.window.embedding.SplitPairFilter: android.content.ComponentName primaryActivityName
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
androidx.window.layout.FoldingFeature$State: java.lang.String description
androidx.window.layout.FoldingFeature$Orientation: androidx.window.layout.FoldingFeature$Orientation$Companion Companion
androidx.window.layout.SidecarCompat$DistinctElementCallback: androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface callbackInterface
androidx.window.layout.ExtensionWindowLayoutInfoBackend: java.util.Map activityToListeners
androidx.window.R$styleable: int[] SplitPairFilter
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter: androidx.window.layout.WindowInfoTracker tracker
androidx.window.core.Version: int patch
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
androidx.window.R$attr: int finishPrimaryWithSecondary
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: android.app.Activity activity
androidx.window.R$attr: int activityAction
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
androidx.window.core.Version: androidx.window.core.Version$Companion Companion
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
androidx.window.R$attr: int splitLayoutDirection
io.flutter.plugin.platform.SingleViewPresentation: int viewId
androidx.window.R$attr: int secondaryActivityAction
kotlinx.coroutines.CancelledContinuation: int _resumed
androidx.window.embedding.SplitRule: float splitRatio
androidx.fragment.app.FragmentManager$LaunchedFragmentInfo: android.os.Parcelable$Creator CREATOR
androidx.window.layout.SidecarAdapter: androidx.window.layout.SidecarAdapter$Companion Companion
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
kotlinx.coroutines.sync.MutexImpl$LockWaiter: java.lang.Object isTaken
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter: java.util.concurrent.locks.ReentrantLock lock
androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper: java.util.List lastValue
androidx.window.layout.FoldingFeature$State: androidx.window.layout.FoldingFeature$State$Companion Companion
androidx.window.core.Version: int minor
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String CACHE_DIRECTORY_BASE_PATH
androidx.window.R$styleable: int[] SplitPlaceholderRule
kotlin.jvm.internal.CallableReference: java.lang.Object receiver
androidx.window.core.Bounds: int top
kotlin.jvm.internal.FunctionReference: int arity
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
androidx.window.core.Version: androidx.window.core.Version CURRENT
androidx.window.layout.WindowMetricsCalculator: androidx.window.layout.WindowMetricsCalculator$Companion Companion
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
androidx.window.layout.WindowInfoTrackerImpl: androidx.window.layout.WindowMetricsCalculator windowMetricsCalculator
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
androidx.window.R$styleable: int ActivityFilter_activityAction
androidx.window.core.Bounds: int bottom
androidx.window.layout.WindowInfoTracker$Companion: java.lang.String TAG
androidx.window.embedding.SplitPairRule: boolean finishPrimaryWithSecondary
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
androidx.window.embedding.MatcherUtils: androidx.window.embedding.MatcherUtils INSTANCE
androidx.window.layout.WindowMetricsCalculatorCompat: androidx.window.layout.WindowMetricsCalculatorCompat INSTANCE
androidx.window.layout.DisplayCompatHelperApi28: androidx.window.layout.DisplayCompatHelperApi28 INSTANCE
androidx.window.R$attr: int splitMinWidth
androidx.window.embedding.SplitController: java.util.concurrent.locks.ReentrantLock globalLock
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback: java.util.concurrent.locks.ReentrantLock lock
androidx.window.layout.HardwareFoldingFeature: androidx.window.layout.HardwareFoldingFeature$Type type
androidx.window.embedding.SplitController: androidx.window.embedding.EmbeddingBackend embeddingBackend
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
androidx.window.embedding.SplitPairRule: boolean getFinishPrimaryWithSecondary()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
androidx.appcompat.widget.AppCompatSpinner: androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup getInternalPopup()
androidx.appcompat.widget.AppCompatSpinner: android.graphics.drawable.Drawable getPopupBackground()
androidx.window.layout.ExtensionInterfaceCompat: void onWindowLayoutChangeListenerAdded(android.app.Activity)
androidx.datastore.core.State: State()
androidx.window.layout.SidecarCompat: void setExtensionCallback(androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface)
kotlin.jvm.internal.CallableReference: kotlin.reflect.KCallable computeReflected()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.util.List getAllProfileNames()
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter: kotlinx.coroutines.flow.Flow windowLayoutInfo(android.app.Activity)
androidx.core.content.ContextCompat$Api16Impl: void startActivities(android.content.Context,android.content.Intent[],android.os.Bundle)
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
androidx.window.layout.EmptyDecorator: androidx.window.layout.WindowInfoTracker decorate(androidx.window.layout.WindowInfoTracker)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatEditText: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.window.embedding.EmbeddingAdapter: boolean translateParentMetricsPredicate$lambda-4(androidx.window.embedding.SplitRule,android.view.WindowMetrics)
kotlinx.coroutines.EventLoopImplBase: EventLoopImplBase()
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
androidx.window.embedding.ActivityFilter: boolean equals(java.lang.Object)
androidx.activity.ComponentActivity: void setContentView(android.view.View)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: boolean isMultiProcessEnabled()
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: ExtensionWindowLayoutInfoBackend$MulticastConsumer(android.app.Activity)
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
androidx.window.layout.WindowMetricsCalculator$Companion$decorator$1: androidx.window.layout.WindowMetricsCalculator invoke(androidx.window.layout.WindowMetricsCalculator)
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
androidx.webkit.internal.ApiHelperForO: android.webkit.WebChromeClient getWebChromeClient(android.webkit.WebView)
androidx.window.embedding.ExtensionEmbeddingBackend: void getSplitChangeCallbacks$annotations()
androidx.window.layout.WindowLayoutInfo: WindowLayoutInfo(java.util.List)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.window.layout.SidecarAdapter: androidx.window.layout.WindowLayoutInfo translate(androidx.window.sidecar.SidecarWindowLayoutInfo,androidx.window.sidecar.SidecarDeviceState)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: void accept(androidx.window.extensions.layout.WindowLayoutInfo)
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1$invokeSuspend$$inlined$collect$1: java.lang.Object emit(java.lang.Object,kotlin.coroutines.Continuation)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
kotlin.jvm.internal.CallableReference: java.lang.String getName()
androidx.window.embedding.SplitInfo: int hashCode()
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
androidx.window.embedding.EmbeddingRule: EmbeddingRule()
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
androidx.fragment.app.FragmentActivity: FragmentActivity()
androidx.window.embedding.SplitPairRule: SplitPairRule(java.util.Set,boolean,boolean,boolean,int,int,float,int,int,kotlin.jvm.internal.DefaultConstructorMarker)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
androidx.appcompat.widget.AbsActionBarView: int getAnimatedVisibility()
androidx.appcompat.widget.ScrollingTabContainerView: void setAllowCollapse(boolean)
androidx.appcompat.widget.AppCompatSpinner: int getDropDownWidth()
androidx.window.layout.SidecarCompat$FirstAttachAdapter: SidecarCompat$FirstAttachAdapter(androidx.window.layout.SidecarCompat,android.app.Activity)
androidx.core.view.accessibility.AccessibilityViewCommand$MoveAtGranularityArguments: AccessibilityViewCommand$MoveAtGranularityArguments()
androidx.datastore.preferences.core.Preferences: Preferences()
androidx.window.layout.EmptyDecorator: EmptyDecorator()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.ParcelFileDescriptor openFile(android.content.ContentProvider,android.net.Uri)
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
androidx.window.layout.HardwareFoldingFeature$Type$Companion: androidx.window.layout.HardwareFoldingFeature$Type getHINGE()
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMinTextSize()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
androidx.window.layout.ActivityCompatHelperApi30: android.graphics.Rect currentWindowBounds(android.app.Activity)
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.datastore.preferences.protobuf.FloatArrayList: FloatArrayList()
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact[] values()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
androidx.appcompat.widget.SearchView: int getPreferredWidth()
androidx.window.layout.SidecarCompat$FirstAttachAdapter: void onViewAttachedToWindow(android.view.View)
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.window.embedding.SplitPairFilter: android.content.ComponentName getPrimaryActivityName()
androidx.window.layout.SidecarWindowBackend$ExtensionListenerImpl: void onWindowLayoutChanged(android.app.Activity,androidx.window.layout.WindowLayoutInfo)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.collection.LongSparseArray: LongSparseArray()
androidx.window.layout.WindowMetricsCalculator$Companion$reset$1: androidx.window.layout.WindowMetricsCalculator invoke(androidx.window.layout.WindowMetricsCalculator)
io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$ConsoleMessage: GeneratedAndroidWebView$ConsoleMessage()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setAudioMuted(boolean)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
androidx.window.embedding.SplitController$Companion: androidx.window.embedding.SplitController getInstance()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
androidx.window.embedding.SplitInfo: androidx.window.embedding.ActivityStack getSecondaryActivityStack()
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
androidx.appcompat.widget.AppCompatCheckBox: int getCompoundPaddingLeft()
androidx.window.layout.HardwareFoldingFeature$Type: androidx.window.layout.HardwareFoldingFeature$Type access$getFOLD$cp()
androidx.datastore.preferences.protobuf.CodedInputStream: CodedInputStream()
androidx.window.embedding.SplitPairRule: int hashCode()
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: boolean isHorizontallyScrollable(android.widget.TextView)
androidx.window.layout.SidecarCompat$FirstAttachAdapter: void onViewDetachedFromWindow(android.view.View)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceRequest(android.webkit.WebResourceRequest)
androidx.datastore.preferences.protobuf.ExtensionSchema: ExtensionSchema()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
androidx.window.layout.SidecarWindowBackend: void access$setGlobalInstance$cp(androidx.window.layout.SidecarWindowBackend)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setProfile(java.lang.String)
androidx.window.embedding.SplitController: void setStaticSplitRules(java.util.Set)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebViewMediaIntegrityApiDefaultStatus()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
androidx.window.layout.SidecarCompat: androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface access$getExtensionCallback$p(androidx.window.layout.SidecarCompat)
androidx.fragment.app.FragmentTransitionImpl: FragmentTransitionImpl()
androidx.webkit.internal.ApiHelperForOMR1: void startSafeBrowsing(android.content.Context,android.webkit.ValueCallback)
androidx.window.layout.ExtensionInterfaceCompat: void onWindowLayoutChangeListenerRemoved(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertSafeBrowsingResponse(java.lang.reflect.InvocationHandler)
kotlin.jvm.internal.FunctionReference: FunctionReference(int,java.lang.Object)
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
androidx.window.layout.WindowMetricsCalculator: androidx.window.layout.WindowMetrics computeMaximumWindowMetrics(android.app.Activity)
androidx.appcompat.widget.AbsActionBarView: AbsActionBarView(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.net.Uri cache(byte[],java.lang.String,java.lang.String)
kotlin.coroutines.AbstractCoroutineContextElement: kotlin.coroutines.CoroutineContext minusKey(kotlin.coroutines.CoroutineContext$Key)
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
kotlin.jvm.internal.Lambda: Lambda(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
androidx.window.embedding.ExtensionEmbeddingBackend$EmbeddingCallbackImpl: java.util.List getLastInfo()
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
androidx.window.embedding.EmbeddingAdapter: java.util.function.Predicate translateActivityIntentPredicates(java.util.Set)
androidx.window.layout.HardwareFoldingFeature: androidx.window.layout.FoldingFeature$OcclusionType getOcclusionType()
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface: java.lang.String[] getSupportedFeatures()
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertCookieManager(java.lang.Object)
kotlin.coroutines.jvm.internal.ContinuationImpl: kotlin.coroutines.Continuation intercepted()
androidx.window.embedding.EmbeddingBackend: void registerSplitListenerForActivity(android.app.Activity,java.util.concurrent.Executor,androidx.core.util.Consumer)
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
androidx.appcompat.widget.AppCompatEditText: void setTextClassifier(android.view.textclassifier.TextClassifier)
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportBackgroundTintList()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowContentAccess()
androidx.window.embedding.ActivityFilter: java.lang.String toString()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundResource(int)
io.flutter.plugins.webviewflutter.WebViewHostApiImpl$WebViewPlatformView: void setWebViewClient(android.webkit.WebViewClient)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
io.flutter.embedding.engine.FlutterEngineGroupCache: FlutterEngineGroupCache()
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
androidx.webkit.internal.ApiHelperForM: boolean getOffscreenPreRaster(android.webkit.WebSettings)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat: VectorDrawableCompat()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
androidx.window.layout.SidecarWindowBackend: java.util.concurrent.locks.ReentrantLock access$getGlobalLock$cp()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setUserAgentMetadataFromMap(java.util.Map)
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
androidx.fragment.app.Fragment: Fragment()
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessage(java.lang.String)
androidx.window.layout.SidecarCompat: SidecarCompat(android.content.Context)
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
kotlin.coroutines.jvm.internal.ContinuationImpl: ContinuationImpl(kotlin.coroutines.Continuation)
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
androidx.core.view.accessibility.AccessibilityViewCommand$CommandArguments: AccessibilityViewCommand$CommandArguments()
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
kotlin.jvm.internal.FunctionReference: kotlin.reflect.KFunction getReflected()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
androidx.window.embedding.ActivityFilter: boolean matchesIntent(android.content.Intent)
androidx.window.embedding.SplitController: androidx.window.embedding.SplitController getInstance()
kotlin.jvm.internal.CallableReference: java.lang.Object callBy(java.util.Map)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.window.layout.SidecarWindowBackend: androidx.window.layout.ExtensionInterfaceCompat getWindowExtension()
androidx.lifecycle.ViewModelProvider$KeyedFactory: ViewModelProvider$KeyedFactory()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
androidx.appcompat.view.ContextThemeWrapper: ContextThemeWrapper()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setCacheMode(int)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$WebViewPoint: GeneratedAndroidWebView$WebViewPoint()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void initSafeBrowsing(android.content.Context,android.webkit.ValueCallback)
androidx.window.layout.SidecarWindowBackend: void getWindowLayoutChangeCallbacks$annotations()
androidx.window.embedding.ActivityRule: boolean getAlwaysExpand()
androidx.window.layout.FoldingFeature: boolean isSeparating()
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter: void removeWindowLayoutInfoListener(androidx.core.util.Consumer)
androidx.appcompat.widget.AppCompatToggleButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: kotlin.coroutines.Continuation create(java.lang.Object,kotlin.coroutines.Continuation)
androidx.core.view.accessibility.AccessibilityViewCommand$SetSelectionArguments: AccessibilityViewCommand$SetSelectionArguments()
androidx.datastore.preferences.protobuf.AbstractMessageLite: AbstractMessageLite()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.android.HandlerContext createDispatcher(java.util.List)
androidx.appcompat.widget.DropDownListView: void setSelectorEnabled(boolean)
androidx.appcompat.app.AppCompatActivity: void setContentView(android.view.View)
androidx.window.layout.ExtensionWindowLayoutInfoBackend: void registerLayoutChangeCallback(android.app.Activity,java.util.concurrent.Executor,androidx.core.util.Consumer)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
androidx.window.layout.HardwareFoldingFeature$Companion: HardwareFoldingFeature$Companion()
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
androidx.webkit.internal.ApiHelperForP: boolean isTracing(android.webkit.TracingController)
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
kotlinx.coroutines.channels.Receive: Receive()
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
androidx.window.embedding.EmbeddingBackend: void setSplitRules(java.util.Set)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
kotlinx.coroutines.scheduling.Task: Task()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: boolean isAudioMuted()
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler getMessagePayload()
androidx.window.embedding.SplitPairFilter: android.content.ComponentName getSecondaryActivityName()
androidx.window.embedding.SplitPairFilter: int hashCode()
androidx.window.embedding.SplitRule: int getMinWidth()
androidx.window.layout.WindowMetricsCalculatorCompat: android.graphics.Rect computeWindowBoundsQ$window_release(android.app.Activity)
androidx.core.view.accessibility.AccessibilityViewCommand$MoveWindowArguments: AccessibilityViewCommand$MoveWindowArguments()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.window.layout.FoldingFeature$Orientation$Companion: FoldingFeature$Orientation$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: android.net.Uri getSafeBrowsingPrivacyPolicyUrl()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.datastore.preferences.protobuf.FieldSet: FieldSet()
androidx.datastore.preferences.protobuf.LongArrayList: LongArrayList()
androidx.appcompat.widget.AppCompatSpinner: int getDropDownVerticalOffset()
io.flutter.plugins.webviewflutter.WebViewHostApiImpl$WebViewPlatformView: void setApi(io.flutter.plugins.webviewflutter.WebViewFlutterApiImpl)
kotlin.jvm.internal.Intrinsics: Intrinsics()
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
androidx.window.R$styleable: R$styleable()
kotlinx.coroutines.EventLoop: EventLoop()
androidx.window.layout.SidecarAdapter: boolean isEqualSidecarDisplayFeature(androidx.window.sidecar.SidecarDisplayFeature,androidx.window.sidecar.SidecarDisplayFeature)
androidx.webkit.internal.ApiHelperForP: android.os.Looper getWebViewLooper(android.webkit.WebView)
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VObject: VectorDrawableCompat$VObject()
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface: android.webkit.WebResourceResponse shouldInterceptRequest(android.webkit.WebResourceRequest)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
androidx.window.embedding.ExtensionEmbeddingBackend$Companion: boolean isExtensionVersionSupported(java.lang.Integer)
androidx.window.layout.WindowMetricsCalculator$Companion: androidx.window.layout.WindowMetricsCalculator getOrCreate()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: boolean onCreate()
androidx.window.layout.WindowInfoTracker$Companion: androidx.window.layout.WindowInfoTracker getOrCreate(android.content.Context)
androidx.datastore.preferences.protobuf.GeneratedMessageLite: GeneratedMessageLite()
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
androidx.window.core.Version: boolean equals(java.lang.Object)
androidx.window.layout.SidecarAdapter$Companion: SidecarAdapter$Companion()
androidx.window.layout.WindowInfoTrackerImpl: androidx.window.layout.WindowBackend access$getWindowBackend$p(androidx.window.layout.WindowInfoTrackerImpl)
kotlin.coroutines.jvm.internal.BaseContinuationImpl: BaseContinuationImpl(kotlin.coroutines.Continuation)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getDropDataProvider()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper: void accept$lambda-1(androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper,java.util.List)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
kotlin.collections.IntIterator: IntIterator()
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebMessagePort(java.lang.Object)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
androidx.window.embedding.SplitInfo: boolean contains(android.app.Activity)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
kotlinx.coroutines.internal.OpDescriptor: OpDescriptor()
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.window.layout.WindowMetricsCalculatorCompat: android.graphics.Rect computeWindowBoundsN$window_release(android.app.Activity)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertServiceWorkerSettings(java.lang.Object)
kotlin.coroutines.AbstractCoroutineContextElement: kotlin.coroutines.CoroutineContext$Element get(kotlin.coroutines.CoroutineContext$Key)
androidx.core.os.CancellationSignal: CancellationSignal()
androidx.webkit.internal.ApiHelperForM: androidx.webkit.WebMessageCompat createWebMessageCompat(android.webkit.WebMessage)
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.SpinnerAdapter)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
androidx.appcompat.widget.AppCompatSpinner: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRenderer()
androidx.window.embedding.ActivityStack: java.util.List getActivities$window_release()
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: void setServiceWorkerClient(java.lang.reflect.InvocationHandler)
androidx.window.embedding.ExtensionEmbeddingBackend: java.util.concurrent.locks.ReentrantLock access$getGlobalLock$cp()
androidx.window.embedding.SplitPairRule: androidx.window.embedding.SplitPairRule plus$window_release(androidx.window.embedding.SplitPairFilter)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.window.layout.SidecarWindowBackend$Companion: androidx.window.layout.ExtensionInterfaceCompat initAndVerifyExtension(android.content.Context)
androidx.window.embedding.SplitController: void initialize(android.content.Context,int)
androidx.window.embedding.SplitController: void addSplitListener(android.app.Activity,java.util.concurrent.Executor,androidx.core.util.Consumer)
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: androidx.core.util.Consumer getCallback()
androidx.window.embedding.SplitInfo: boolean equals(java.lang.Object)
androidx.window.layout.WindowMetricsCalculator$Companion$overrideDecorator$1: androidx.window.layout.WindowMetricsCalculator invoke(androidx.window.layout.WindowMetricsCalculator)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSafeBrowsingResponse(java.lang.Object)
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
androidx.window.core.Version$Companion: androidx.window.core.Version getVERSION_1_0()
androidx.window.embedding.SplitRuleParser: java.util.Set parseSplitRules$window_release(android.content.Context,int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.datastore.preferences.core.MutablePreferences: MutablePreferences()
kotlin.jvm.internal.CallableReference: boolean isFinal()
kotlinx.coroutines.CancelHandlerBase: CancelHandlerBase()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.datastore.preferences.protobuf.DoubleArrayList: DoubleArrayList()
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
androidx.window.embedding.SplitPairFilter: SplitPairFilter(android.content.ComponentName,android.content.ComponentName,java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.webkit.internal.ApiHelperForP: android.webkit.TracingController getTracingControllerInstance()
androidx.window.layout.ExtensionsWindowLayoutInfoAdapter: ExtensionsWindowLayoutInfoAdapter()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.window.layout.SidecarAdapter: java.util.List translate(java.util.List,androidx.window.sidecar.SidecarDeviceState)
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: int getErrorCode()
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.window.layout.ActivityCompatHelperApi30: ActivityCompatHelperApi30()
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
kotlin.coroutines.jvm.internal.BaseContinuationImpl: kotlin.coroutines.jvm.internal.CoroutineStackFrame getCallerFrame()
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1: kotlin.coroutines.Continuation create(java.lang.Object,kotlin.coroutines.Continuation)
kotlin.jvm.internal.FunctionReference: boolean isInline()
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.appcompat.widget.SearchView: int getImeOptions()
androidx.datastore.preferences.protobuf.BooleanArrayList: BooleanArrayList()
androidx.window.layout.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void postMessage(java.lang.reflect.InvocationHandler)
androidx.window.layout.SidecarCompat$registerConfigurationChangeListener$configChangeObserver$1: void onConfigurationChanged(android.content.res.Configuration)
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.core.widget.NestedScrollView: int getScrollRange()
io.flutter.plugins.sharedpreferences.SharedPreferencesPigeonOptions: SharedPreferencesPigeonOptions()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.window.embedding.EmbeddingBackend: void unregisterSplitListenerForActivity(androidx.core.util.Consumer)
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: java.lang.Object invoke(java.lang.Object,java.lang.Object)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDark()
androidx.window.embedding.SplitRule$Api30Impl: SplitRule$Api30Impl()
androidx.window.embedding.SplitController: SplitController(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1$invokeSuspend$$inlined$collect$1: WindowInfoTrackerCallbackAdapter$addListener$1$1$invokeSuspend$$inlined$collect$1(androidx.core.util.Consumer)
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
androidx.window.embedding.MatcherUtils: boolean wildcardMatch(java.lang.String,java.lang.String)
androidx.window.layout.SidecarCompat: java.util.Map access$getWindowListenerRegisteredContexts$p(androidx.window.layout.SidecarCompat)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface: boolean isRedirect()
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRendererClient()
androidx.window.embedding.EmbeddingBackend: void unregisterRule(androidx.window.embedding.EmbeddingRule)
androidx.window.layout.SidecarWindowBackend$Companion: androidx.window.layout.SidecarWindowBackend getInstance(android.content.Context)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.window.layout.HardwareFoldingFeature$Companion: void validateFeatureBounds$window_release(androidx.window.core.Bounds)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State valueOf(java.lang.String)
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: void accept$lambda-0(androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper,androidx.window.layout.WindowLayoutInfo)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
androidx.window.layout.SidecarWindowBackend: boolean isActivityRegistered(android.app.Activity)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSettings(android.webkit.WebSettings)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.window.core.Version: int hashCode()
androidx.datastore.preferences.PreferencesProto$StringSet: PreferencesProto$StringSet()
androidx.window.layout.SidecarCompat: void onWindowLayoutChangeListenerAdded(android.app.Activity)
kotlin.jvm.internal.FunctionReference: java.lang.String toString()
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.window.layout.WindowMetricsCalculator$Companion: void overrideDecorator(androidx.window.layout.WindowMetricsCalculatorDecorator)
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: void accept(androidx.window.layout.WindowLayoutInfo)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
androidx.appcompat.widget.AbsActionBarView: void setContentHeight(int)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.window.layout.WindowMetricsCalculatorCompat: int getNavigationBarHeight(android.content.Context)
kotlin.jvm.internal.Lambda: int getArity()
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.window.embedding.SplitController: boolean isSplitSupported()
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: boolean deleteProfile(java.lang.String)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.window.embedding.SplitInfo: java.lang.String toString()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
androidx.window.layout.WindowMetricsCalculatorCompat: androidx.window.layout.WindowMetrics computeMaximumWindowMetrics(android.app.Activity)
androidx.window.embedding.ExtensionEmbeddingBackend: void unregisterSplitListenerForActivity(androidx.core.util.Consumer)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getWillSuppressErrorPage()
kotlin.jvm.internal.CallableReference: kotlin.reflect.KCallable compute()
androidx.window.embedding.SplitRule: float getSplitRatio()
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.Adapter)
androidx.window.embedding.SplitInfo: float getSplitRatio()
androidx.window.embedding.ExtensionEmbeddingBackend$EmbeddingCallbackImpl: ExtensionEmbeddingBackend$EmbeddingCallbackImpl(androidx.window.embedding.ExtensionEmbeddingBackend)
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
androidx.window.layout.WindowMetricsCalculator$Companion$overrideDecorator$1: WindowMetricsCalculator$Companion$overrideDecorator$1(java.lang.Object)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.window.layout.SidecarWindowBackend: void setWindowExtension(androidx.window.layout.ExtensionInterfaceCompat)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowContentAccess(boolean)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: AppCompatAutoCompleteTextView(android.content.Context,android.util.AttributeSet)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.webkit.internal.ApiHelperForP: void setDataDirectorySuffix(java.lang.String)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebResourceError(java.lang.reflect.InvocationHandler)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
kotlin.UninitializedPropertyAccessException: UninitializedPropertyAccessException()
androidx.webkit.internal.ApiHelperForO: boolean getSafeBrowsingEnabled(android.webkit.WebSettings)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
kotlinx.coroutines.JobNode: JobNode()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebViewMediaIntegrityApiStatus(int,java.util.Map)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
androidx.window.embedding.EmbeddingInterfaceCompat: void setEmbeddingCallback(androidx.window.embedding.EmbeddingInterfaceCompat$EmbeddingCallbackInterface)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler,android.os.Handler)
androidx.window.embedding.ExtensionEmbeddingBackend: boolean isSplitSupported()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
androidx.window.embedding.SplitPairFilter: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
androidx.window.layout.DisplayCompatHelperApi17: void getRealSize(android.view.Display,android.graphics.Point)
androidx.window.core.Bounds: int getBottom()
androidx.window.embedding.SplitPlaceholderRule: boolean equals(java.lang.Object)
androidx.window.layout.FoldingFeature$OcclusionType: java.lang.String toString()
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.window.layout.ExtensionWindowLayoutInfoBackend: ExtensionWindowLayoutInfoBackend(androidx.window.extensions.layout.WindowLayoutComponent)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
androidx.appcompat.widget.Toolbar: void setTitle(int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler[] createWebMessageChannel()
androidx.window.layout.HardwareFoldingFeature: androidx.window.layout.FoldingFeature$State getState()
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.appcompat.app.ActionBar$Tab: ActionBar$Tab()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface: void onMessage(java.lang.reflect.InvocationHandler,java.lang.reflect.InvocationHandler)
androidx.window.embedding.EmbeddingCompat$Companion: EmbeddingCompat$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.activity.ComponentActivity$NonConfigurationInstances: ComponentActivity$NonConfigurationInstances()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.FlutterJNI: void loadLibrary()
androidx.window.core.Version: int compareTo(androidx.window.core.Version)
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.window.layout.HardwareFoldingFeature: HardwareFoldingFeature(androidx.window.core.Bounds,androidx.window.layout.HardwareFoldingFeature$Type,androidx.window.layout.FoldingFeature$State)
androidx.window.layout.SidecarWindowBackend: void callbackRemovedForActivity(android.app.Activity)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: boolean shouldOverrideUrlLoading(android.webkit.WebView,android.webkit.WebResourceRequest)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
androidx.appcompat.widget.AppCompatButton: int getAutoSizeTextType()
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: java.lang.Object invoke(kotlinx.coroutines.flow.FlowCollector,kotlin.coroutines.Continuation)
androidx.window.layout.SidecarWindowBackend: java.util.concurrent.CopyOnWriteArrayList getWindowLayoutChangeCallbacks()
kotlinx.coroutines.CoroutineDispatcher: CoroutineDispatcher()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.window.embedding.ActivityFilter: java.lang.String getIntentAction()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
androidx.window.embedding.ActivityRule: ActivityRule(java.util.Set,boolean,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.appcompat.widget.AppCompatToggleButton: void setBackgroundResource(int)
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.window.core.Version$Companion: Version$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.appcompat.widget.LinearLayoutCompat: LinearLayoutCompat(android.content.Context,android.util.AttributeSet,int)
com.lib.flutter_blue_plus.FlutterBluePlusPlugin$LogLevel: com.lib.flutter_blue_plus.FlutterBluePlusPlugin$LogLevel valueOf(java.lang.String)
androidx.window.embedding.SplitRuleParser: androidx.window.embedding.ActivityRule parseSplitActivityRule(android.content.Context,android.content.res.XmlResourceParser)
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1: java.lang.Object invoke(kotlinx.coroutines.CoroutineScope,kotlin.coroutines.Continuation)
androidx.window.embedding.EmbeddingAdapter: java.util.Set translate(java.util.Set)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.window.core.Version: androidx.window.core.Version access$getUNKNOWN$cp()
androidx.lifecycle.ViewModelStore: ViewModelStore()
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
kotlin.internal.jdk8.JDK8PlatformImplementations: JDK8PlatformImplementations()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.webkit.internal.ApiHelperForOMR1: void showInterstitial(android.webkit.SafeBrowsingResponse,boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
androidx.window.layout.SidecarAdapter: boolean isEqualSidecarWindowLayoutInfo(androidx.window.sidecar.SidecarWindowLayoutInfo,androidx.window.sidecar.SidecarWindowLayoutInfo)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
kotlinx.coroutines.flow.internal.AbstractSharedFlowSlot: AbstractSharedFlowSlot()
kotlin.coroutines.jvm.internal.ContinuationImpl: kotlin.coroutines.CoroutineContext getContext()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.android.FlutterActivity: FlutterActivity()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void onDragEnd(boolean)
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.window.embedding.SplitRuleParser: java.util.Set parseSplitXml(android.content.Context,int)
androidx.window.layout.WindowMetrics: WindowMetrics(android.graphics.Rect)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getProfile()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getOffscreenPreRaster()
androidx.window.layout.SidecarCompat: androidx.window.layout.WindowLayoutInfo getWindowLayoutInfo(android.app.Activity)
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: WindowInfoTrackerImpl$windowLayoutInfo$1(androidx.window.layout.WindowInfoTrackerImpl,android.app.Activity,kotlin.coroutines.Continuation)
androidx.appcompat.widget.AppCompatTextView: AppCompatTextView(android.content.Context,android.util.AttributeSet)
kotlin.jvm.internal.CallableReference: java.lang.String getSignature()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.window.embedding.EmbeddingCompat: EmbeddingCompat(androidx.window.extensions.embedding.ActivityEmbeddingComponent,androidx.window.embedding.EmbeddingAdapter)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.webkit.internal.ApiHelperForP: boolean stop(android.webkit.TracingController,java.io.OutputStream,java.util.concurrent.Executor)
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(android.graphics.drawable.Drawable)
androidx.webkit.internal.ApiHelperForM: void close(android.webkit.WebMessagePort)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1: WindowInfoTrackerCallbackAdapter$addListener$1$1(kotlinx.coroutines.flow.Flow,androidx.core.util.Consumer,kotlin.coroutines.Continuation)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.WebStorage getWebStorage()
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
androidx.appcompat.widget.AppCompatButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertServiceWorkerSettings(java.lang.reflect.InvocationHandler)
androidx.window.core.Bounds: int getTop()
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.webkit.internal.ApiHelperForM: void setOffscreenPreRaster(android.webkit.WebSettings,boolean)
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: void accept(java.lang.Object)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: java.lang.Object invokeSuspend(java.lang.Object)
androidx.window.embedding.EmptyEmbeddingComponent: void setEmbeddingRules(java.util.Set)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceError(java.lang.Object)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.database.Cursor query(android.net.Uri,java.lang.String[],java.lang.String,java.lang.String[],java.lang.String)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void addWebMessageListener(java.lang.String,java.lang.String[],java.lang.reflect.InvocationHandler)
androidx.window.layout.WindowLayoutInfo: int hashCode()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.GeolocationPermissions getGeoLocationPermissions()
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
androidx.window.R$id: R$id()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.window.embedding.SplitController: java.util.Set getSplitRules()
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface: void remove()
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(int)
io.flutter.view.AccessibilityBridge$LocaleStringAttribute: AccessibilityBridge$LocaleStringAttribute()
kotlin.jvm.internal.CallableReference: CallableReference()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.window.embedding.EmbeddingAdapter: boolean translateActivityIntentPredicates$lambda-3(androidx.window.embedding.EmbeddingAdapter,java.util.Set,android.util.Pair)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.window.embedding.ActivityStack: int hashCode()
androidx.appcompat.widget.ScrollingTabContainerView: void setTabSelected(int)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
kotlinx.coroutines.YieldContext: YieldContext()
kotlin.jvm.internal.CallableReference: CallableReference(java.lang.Object,java.lang.Class,java.lang.String,java.lang.String,boolean)
androidx.window.layout.WindowMetricsCalculator: androidx.window.layout.WindowMetrics computeCurrentWindowMetrics(android.app.Activity)
androidx.window.layout.WindowMetrics: int hashCode()
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: VectorDrawableCompat$VPath()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.arch.core.internal.SafeIterableMap: SafeIterableMap()
androidx.window.embedding.ExtensionEmbeddingBackend: void setSplitRules(java.util.Set)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
kotlin.jvm.internal.FunctionReference: int hashCode()
kotlinx.coroutines.ExecutorCoroutineDispatcher: ExecutorCoroutineDispatcher()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
androidx.window.embedding.EmbeddingAdapter: boolean translateIntentPredicates$lambda-8(java.util.Set,android.content.Intent)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$FileChooserMode: io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$FileChooserMode valueOf(java.lang.String)
androidx.window.layout.WindowMetricsCalculator$-CC: void overrideDecorator(androidx.window.layout.WindowMetricsCalculatorDecorator)
kotlin.coroutines.jvm.internal.SuspendLambda: SuspendLambda(int)
androidx.window.core.Bounds: int getHeight()
androidx.window.embedding.ActivityRule: java.util.Set getFilters()
androidx.window.embedding.ExtensionEmbeddingBackend: androidx.window.embedding.EmbeddingInterfaceCompat getEmbeddingExtension()
io.flutter.view.AccessibilityBridge$StringAttribute: AccessibilityBridge$StringAttribute()
io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$ConsoleMessageLevel: io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$ConsoleMessageLevel[] values()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
androidx.appcompat.widget.AppCompatButton: void setBackgroundResource(int)
androidx.datastore.preferences.PreferencesProto$Value: PreferencesProto$Value()
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void postMessageToMainFrame(java.lang.reflect.InvocationHandler,android.net.Uri)
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
io.flutter.plugins.webviewflutter.WebViewHostApiImpl$WebViewPlatformView: android.view.View getView()
androidx.window.layout.ActivityCompatHelperApi24: ActivityCompatHelperApi24()
androidx.window.layout.WindowInfoTracker$Companion: void reset()
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
androidx.window.layout.SidecarCompat: void unregisterComponentCallback(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
androidx.window.layout.SidecarAdapter: java.lang.String access$getTAG$cp()
kotlin.jvm.internal.CallableReference: boolean isSuspend()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.app.AppCompatDelegate: AppCompatDelegate()
kotlin.jvm.internal.Ref$ObjectRef: Ref$ObjectRef()
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
androidx.window.layout.WindowInfoTracker$-CC: androidx.window.layout.WindowInfoTracker getOrCreate(android.content.Context)
androidx.window.layout.SidecarWindowBackend: void unregisterLayoutChangeCallback(androidx.core.util.Consumer)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: AppCompatTextViewAutoSizeHelper$Impl()
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getOrCreateProfile(java.lang.String)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
androidx.window.layout.WindowLayoutInfo: boolean equals(java.lang.Object)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
androidx.appcompat.widget.SearchView: int getPreferredHeight()
androidx.core.view.accessibility.AccessibilityViewCommand$MoveHtmlArguments: AccessibilityViewCommand$MoveHtmlArguments()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
androidx.window.core.Version$Companion: androidx.window.core.Version getVERSION_0_1()
kotlin.jvm.internal.ReflectionFactory: ReflectionFactory()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: void setLastInfo(androidx.window.layout.WindowLayoutInfo)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
androidx.window.embedding.EmbeddingTranslatingCallback: EmbeddingTranslatingCallback(androidx.window.embedding.EmbeddingInterfaceCompat$EmbeddingCallbackInterface,androidx.window.embedding.EmbeddingAdapter)
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
androidx.fragment.app.FragmentTransaction$Op: FragmentTransaction$Op()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler createWebView(android.webkit.WebView)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
io.flutter.plugins.sharedpreferences.SharedPreferencesAsyncApi$Companion: SharedPreferencesAsyncApi$Companion()
kotlin.reflect.KVisibility: kotlin.reflect.KVisibility valueOf(java.lang.String)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void clearProxyOverride(java.lang.Runnable,java.util.concurrent.Executor)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAttributionBehavior(int)
androidx.appcompat.widget.AppCompatSpinner: android.content.Context getPopupContext()
androidx.window.layout.DisplayCompatHelperApi28: int safeInsetLeft(android.view.DisplayCutout)
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
androidx.appcompat.widget.AppCompatSpinner: void setDropDownWidth(int)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowFileAccess()
kotlin.coroutines.jvm.internal.BaseContinuationImpl: java.lang.Object invokeSuspend(java.lang.Object)
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
kotlinx.coroutines.internal.ThreadSafeHeap: ThreadSafeHeap()
androidx.datastore.preferences.PreferencesProto$Value$Builder: PreferencesProto$Value$Builder()
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
androidx.window.embedding.ExtensionEmbeddingBackend$Companion: ExtensionEmbeddingBackend$Companion()
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererUnresponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebChromeClient getWebChromeClient()
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProxyController()
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void backToSafety(boolean)
androidx.core.view.accessibility.AccessibilityViewCommand$SetProgressArguments: AccessibilityViewCommand$SetProgressArguments()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
androidx.datastore.preferences.PreferencesProto$PreferenceMap$Builder: PreferencesProto$PreferenceMap$Builder()
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
androidx.window.layout.WindowInfoTrackerImpl$Companion: WindowInfoTrackerImpl$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.window.core.Bounds: boolean isEmpty()
androidx.window.layout.ExtensionWindowLayoutInfoBackend: void unregisterLayoutChangeCallback(androidx.core.util.Consumer)
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: boolean isEmpty()
androidx.window.layout.WindowMetrics: WindowMetrics(androidx.window.core.Bounds)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(int)
kotlin.jvm.internal.CallableReference: boolean isOpen()
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintList(android.content.res.ColorStateList)
kotlin.coroutines.jvm.internal.BaseContinuationImpl: java.lang.String toString()
androidx.appcompat.widget.LinearLayoutCompat: LinearLayoutCompat(android.content.Context)
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.datastore.preferences.protobuf.MapFieldLite: MapFieldLite()
androidx.webkit.internal.ApiHelperForOMR1: android.net.Uri getSafeBrowsingPrivacyPolicyUrl()
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
kotlin.jvm.internal.FunctionReference: FunctionReference(int,java.lang.Object,java.lang.Class,java.lang.String,java.lang.String,int)
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.window.layout.FoldingFeature$OcclusionType$Companion: FoldingFeature$OcclusionType$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
androidx.window.embedding.ExtensionEmbeddingBackend$EmbeddingCallbackImpl: void setLastInfo(java.util.List)
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: boolean onWebAuthnIntent(android.webkit.WebView,android.app.PendingIntent,java.lang.reflect.InvocationHandler)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.window.layout.SidecarCompat: boolean validateExtensionInterface()
androidx.window.layout.SidecarAdapter$Companion: androidx.window.layout.DisplayFeature translate$window_release(androidx.window.sidecar.SidecarDisplayFeature,androidx.window.sidecar.SidecarDeviceState)
androidx.window.embedding.SplitRuleParser: SplitRuleParser()
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
kotlin.jvm.internal.FunctionReference: boolean equals(java.lang.Object)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void setClearCachedDataIntervalMs(int)
androidx.window.layout.FoldingFeature$State$Companion: FoldingFeature$State$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
kotlin.coroutines.jvm.internal.BaseContinuationImpl: kotlin.coroutines.Continuation create(kotlin.coroutines.Continuation)
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State[] values()
androidx.window.layout.WindowInfoTrackerImpl: kotlinx.coroutines.flow.Flow windowLayoutInfo(android.app.Activity)
com.lib.flutter_blue_plus.FlutterBluePlusPlugin$LogLevel: com.lib.flutter_blue_plus.FlutterBluePlusPlugin$LogLevel[] values()
androidx.window.layout.SidecarCompat$Companion: SidecarCompat$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.window.embedding.ExtensionEmbeddingBackend: void unregisterRule(androidx.window.embedding.EmbeddingRule)
androidx.window.layout.SidecarAdapter$Companion: void setSidecarDevicePosture(androidx.window.sidecar.SidecarDeviceState,int)
androidx.window.embedding.SplitRule: int getLayoutDirection()
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
androidx.window.core.Bounds: java.lang.String toString()
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: int getCacheMode()
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
androidx.window.embedding.SplitRule: SplitRule(int,int,float,int,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererResponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
kotlin.jvm.internal.FunctionReference: boolean isExternal()
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingWhitelist(java.util.List,android.webkit.ValueCallback)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void insertVisualStateCallback(long,java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
androidx.webkit.internal.ApiHelperForM: void setWebMessageCallback(android.webkit.WebMessagePort,androidx.webkit.WebMessagePortCompat$WebMessageCallbackCompat,android.os.Handler)
androidx.appcompat.widget.AppCompatEditText: void setBackgroundResource(int)
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
androidx.window.embedding.EmbeddingAdapter: java.lang.Object component2(android.util.Pair)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.datastore.preferences.protobuf.UnknownFieldSchema: UnknownFieldSchema()
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
kotlin.random.Random: Random()
androidx.window.core.Version: androidx.window.core.Version access$getCURRENT$cp()
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.webkit.internal.ApiHelperForOMR1: void proceed(android.webkit.SafeBrowsingResponse,boolean)
androidx.window.R: R()
androidx.webkit.WebViewClientCompat: WebViewClientCompat()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.window.layout.FoldingFeature$State$Companion: FoldingFeature$State$Companion()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor,boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
androidx.window.embedding.ActivityRule: androidx.window.embedding.ActivityRule plus$window_release(androidx.window.embedding.ActivityFilter)
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: void removeListener(androidx.core.util.Consumer)
androidx.window.embedding.ActivityStack: boolean contains(android.app.Activity)
androidx.window.layout.FoldingFeature$State: java.lang.String toString()
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.appcompat.widget.SearchView: int getInputType()
androidx.appcompat.widget.AppCompatEditText: void setBackgroundDrawable(android.graphics.drawable.Drawable)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface: void onWindowLayoutChanged(android.app.Activity,androidx.window.layout.WindowLayoutInfo)
androidx.window.layout.FoldingFeature$OcclusionType: FoldingFeature$OcclusionType(java.lang.String)
androidx.window.layout.HardwareFoldingFeature: boolean equals(java.lang.Object)
androidx.window.embedding.EmbeddingInterfaceCompat: void setSplitRules(java.util.Set)
androidx.lifecycle.SavedStateHandleController$OnRecreation: SavedStateHandleController$OnRecreation()
androidx.window.layout.FoldingFeature$Orientation: FoldingFeature$Orientation(java.lang.String)
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
kotlin.jvm.internal.FunctionReference: int getArity()
kotlin.coroutines.AbstractCoroutineContextElement: AbstractCoroutineContextElement(kotlin.coroutines.CoroutineContext$Key)
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
kotlin.jvm.internal.CallableReference: kotlin.reflect.KType getReturnType()
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedError(android.webkit.WebView,android.webkit.WebResourceRequest,java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.CookieManager getCookieManager()
androidx.window.layout.ActivityCompatHelperApi24: boolean isInMultiWindowMode(android.app.Activity)
androidx.window.embedding.ActivityFilter: int hashCode()
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$500(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper: androidx.core.util.Consumer getCallback()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.datastore.preferences.protobuf.LazyFieldLite: LazyFieldLite()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
androidx.window.layout.HardwareFoldingFeature$Type: HardwareFoldingFeature$Type(java.lang.String)
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface: java.util.List getCookieInfo(java.lang.String)
androidx.window.core.Version: androidx.window.core.Version access$getVERSION_0_1$cp()
androidx.window.layout.SidecarCompat: SidecarCompat(androidx.window.sidecar.SidecarInterface,androidx.window.layout.SidecarAdapter)
kotlinx.coroutines.scheduling.SchedulerTimeSource: SchedulerTimeSource()
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.appcompat.widget.SearchView: int getMaxWidth()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.window.embedding.ExtensionEmbeddingBackend$Companion: ExtensionEmbeddingBackend$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
kotlin.jvm.internal.PropertyReference1: PropertyReference1()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
androidx.window.layout.WindowMetricsCalculator$Companion$reset$1: java.lang.Object invoke(java.lang.Object)
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String[] getStreamTypes(android.net.Uri,java.lang.String)
androidx.window.embedding.ActivityRule: ActivityRule(java.util.Set,boolean)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String getType(android.net.Uri)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.ScrollingTabContainerView: void setContentHeight(int)
kotlin.internal.PlatformImplementations: PlatformImplementations()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: VectorDrawableCompat$VFullPath()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
androidx.datastore.preferences.PreferencesProto$StringSet$Builder: PreferencesProto$StringSet$Builder()
androidx.window.layout.WindowInfoTrackerDecorator: androidx.window.layout.WindowInfoTracker decorate(androidx.window.layout.WindowInfoTracker)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.window.layout.DisplayCompatHelperApi28: int safeInsetTop(android.view.DisplayCutout)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
com.example.gnss_controller_app.MainActivity: MainActivity()
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.AppCompatEditText: android.content.res.ColorStateList getSupportBackgroundTintList()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.Bundle call(java.lang.String,java.lang.String,android.os.Bundle)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean stop(java.io.OutputStream,java.util.concurrent.Executor)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.window.embedding.SplitController$Companion: void initialize(android.content.Context,int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
kotlin.coroutines.jvm.internal.BaseContinuationImpl: java.lang.StackTraceElement getStackTraceElement()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
androidx.window.core.Version$bigInteger$2: java.math.BigInteger invoke()
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
androidx.window.core.Bounds: int getWidth()
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
androidx.window.embedding.SplitController: java.util.concurrent.locks.ReentrantLock access$getGlobalLock$cp()
androidx.window.core.Version: int getMinor()
androidx.window.embedding.SplitInfo: SplitInfo(androidx.window.embedding.ActivityStack,androidx.window.embedding.ActivityStack,float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
androidx.window.layout.WindowMetrics: java.lang.String toString()
androidx.window.R$attr: R$attr()
kotlin.coroutines.jvm.internal.BaseContinuationImpl: kotlin.coroutines.Continuation create(java.lang.Object,kotlin.coroutines.Continuation)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAlgorithmicDarkeningAllowed(boolean)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$WebResourceRequestData: GeneratedAndroidWebView$WebResourceRequestData()
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
androidx.appcompat.widget.AbsActionBarView: int getContentHeight()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDark(int)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.window.embedding.EmbeddingBackend: java.util.Set getSplitRules()
androidx.collection.SparseArrayCompat: SparseArrayCompat()
androidx.window.core.Version: java.math.BigInteger getBigInteger()
androidx.window.embedding.SplitController: void removeSplitListener(androidx.core.util.Consumer)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.window.layout.FoldingFeature$OcclusionType$Companion: FoldingFeature$OcclusionType$Companion()
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWillSuppressErrorPage(boolean)
kotlin.coroutines.jvm.internal.SuspendLambda: SuspendLambda(int,kotlin.coroutines.Continuation)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.ServiceWorkerController getServiceWorkerController()
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getDisabledActionModeMenuItems()
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatRadioButton: int getCompoundPaddingLeft()
androidx.appcompat.widget.AppCompatToggleButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.datastore.preferences.protobuf.ExtensionRegistryLite: ExtensionRegistryLite()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.window.layout.SidecarCompat: androidx.window.layout.SidecarAdapter access$getSidecarAdapter$p(androidx.window.layout.SidecarCompat)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
androidx.window.embedding.SplitRuleParser: androidx.window.embedding.SplitPairRule parseSplitPairRule(android.content.Context,android.content.res.XmlResourceParser)
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintList(android.content.res.ColorStateList)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
androidx.fragment.app.FragmentTransition$FragmentContainerTransition: FragmentTransition$FragmentContainerTransition()
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
kotlin.jvm.internal.TypeIntrinsics: TypeIntrinsics()
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.window.embedding.ActivityStack: boolean isEmpty()
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
kotlin.jvm.internal.FunctionReferenceImpl: FunctionReferenceImpl(int,java.lang.Object,java.lang.Class,java.lang.String,java.lang.String,int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.window.embedding.SplitController: void unregisterRule(androidx.window.embedding.EmbeddingRule)
androidx.appcompat.widget.AppCompatToggleButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
androidx.window.embedding.SplitRule: boolean checkParentMetrics(android.view.WindowMetrics)
androidx.window.core.Bounds: int hashCode()
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
androidx.window.core.Version$Companion: androidx.window.core.Version getUNKNOWN()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
kotlinx.coroutines.android.HandlerDispatcher: HandlerDispatcher()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebauthnSupport()
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
androidx.window.core.Bounds: boolean equals(java.lang.Object)
androidx.appcompat.widget.SearchView: void setInputType(int)
androidx.appcompat.widget.AppCompatSpinner: java.lang.CharSequence getPrompt()
androidx.window.layout.SidecarCompat$registerConfigurationChangeListener$configChangeObserver$1: SidecarCompat$registerConfigurationChangeListener$configChangeObserver$1(androidx.window.layout.SidecarCompat,android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
kotlin.jvm.internal.CallableReference: java.util.List getTypeParameters()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
androidx.window.embedding.ActivityRule: boolean equals(java.lang.Object)
androidx.appcompat.widget.AppCompatEditText: java.lang.CharSequence getText()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
io.flutter.plugins.webviewflutter.JavaScriptChannel: void postMessage(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
androidx.window.embedding.SplitRule: int getMinSmallestWidth()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
kotlin.jvm.internal.CallableReference: CallableReference(java.lang.Object)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
androidx.datastore.preferences.protobuf.IntArrayList: IntArrayList()
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
androidx.webkit.internal.ApiHelperForOMR1: void setSafeBrowsingWhitelist(java.util.List,android.webkit.ValueCallback)
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: byte[] getAsArrayBuffer()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: java.lang.String getAsString()
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.window.embedding.SplitRuleParser: android.content.ComponentName buildClassName(java.lang.String,java.lang.CharSequence)
androidx.window.core.Version$bigInteger$2: java.lang.Object invoke()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
kotlin.jvm.internal.CallableReference: java.lang.Object getBoundReceiver()
androidx.appcompat.widget.Toolbar: void setLogo(int)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.window.core.Version: Version(int,int,int,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
androidx.window.embedding.EmbeddingAdapter: java.util.function.Predicate translateActivityPredicates(java.util.Set)
kotlin.jvm.internal.CallableReference: java.lang.Object call(java.lang.Object[])
androidx.window.layout.WindowBackend: void registerLayoutChangeCallback(android.app.Activity,java.util.concurrent.Executor,androidx.core.util.Consumer)
androidx.window.embedding.SplitPairFilter: boolean matchesActivityPair(android.app.Activity,android.app.Activity)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
androidx.window.embedding.EmptyEmbeddingComponent: void setSplitInfoCallback(java.util.function.Consumer)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
androidx.window.layout.WindowMetricsCalculator$-CC: androidx.window.layout.WindowMetricsCalculator getOrCreate()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.webkit.internal.ApiHelperForM: void postVisualStateCallback(android.webkit.WebView,long,androidx.webkit.WebViewCompat$VisualStateCallback)
androidx.window.layout.WindowMetricsCalculator$-CC: void reset()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.fragment.app.FragmentContainerView: void setDrawDisappearingViewsLast(boolean)
androidx.window.core.Bounds: Bounds(android.graphics.Rect)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
androidx.window.layout.WindowInfoTracker: kotlinx.coroutines.flow.Flow windowLayoutInfo(android.app.Activity)
androidx.window.embedding.EmbeddingAdapter: java.util.function.Predicate translateActivityPairPredicates(java.util.Set)
androidx.window.embedding.ExtensionEmbeddingBackend: void registerRule(androidx.window.embedding.EmbeddingRule)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
androidx.appcompat.widget.AppCompatCheckedTextView: void setCheckMarkDrawable(int)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setBlockNetworkLoads(boolean)
androidx.window.embedding.SplitPairFilter: java.lang.String toString()
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
androidx.window.embedding.SplitController: SplitController()
androidx.window.embedding.ExtensionEmbeddingBackend: void setEmbeddingExtension(androidx.window.embedding.EmbeddingInterfaceCompat)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void proceed(boolean)
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getProfile(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDarkBehavior(int)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
kotlin.coroutines.jvm.internal.ContinuationImpl: ContinuationImpl(kotlin.coroutines.Continuation,kotlin.coroutines.CoroutineContext)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: boolean isHorizontallyScrollable(android.widget.TextView)
androidx.core.content.ContextCompat$Api23Impl: java.lang.String getSystemServiceName(android.content.Context,java.lang.Class)
androidx.window.embedding.EmbeddingCompat: void setEmbeddingCallback(androidx.window.embedding.EmbeddingInterfaceCompat$EmbeddingCallbackInterface)
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
androidx.window.embedding.ActivityFilter: ActivityFilter(android.content.ComponentName,java.lang.String)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
kotlinx.coroutines.internal.LockFreeLinkedListHead: LockFreeLinkedListHead()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
androidx.window.embedding.SplitRuleParser: androidx.window.embedding.SplitPlaceholderRule parseSplitPlaceholderRule(android.content.Context,android.content.res.XmlResourceParser)
androidx.appcompat.widget.SearchView: void setIconified(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.window.layout.SidecarCompat: androidx.window.sidecar.SidecarInterface getSidecar()
androidx.window.embedding.ExtensionEmbeddingBackend: void registerSplitListenerForActivity(android.app.Activity,java.util.concurrent.Executor,androidx.core.util.Consumer)
kotlin.jvm.internal.CallableReference: java.util.List getAnnotations()
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
androidx.window.embedding.SplitPlaceholderRule: androidx.window.embedding.SplitPlaceholderRule plus$window_release(androidx.window.embedding.ActivityFilter)
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerWebSettings()
androidx.core.view.ViewCompat$UnhandledKeyEventManager: ViewCompat$UnhandledKeyEventManager()
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean isTracing()
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer)
androidx.window.embedding.EmbeddingTranslatingCallback: void accept(java.lang.Object)
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
androidx.window.layout.SidecarAdapter$Companion: SidecarAdapter$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.window.embedding.EmbeddingTranslatingCallback: void accept(java.util.List)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
androidx.window.core.Bounds: Bounds(int,int,int,int)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
androidx.webkit.internal.ApiHelperForM: void setWebMessageCallback(android.webkit.WebMessagePort,androidx.webkit.WebMessagePortCompat$WebMessageCallbackCompat)
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.window.layout.WindowMetricsCalculator$Companion$overrideDecorator$1: java.lang.Object invoke(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.webkit.internal.ApiHelperForM: android.webkit.WebMessage createWebMessage(androidx.webkit.WebMessageCompat)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setSafeBrowsingEnabled(boolean)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowFileAccess(boolean)
kotlin.jvm.internal.FunctionReference: boolean isInfix()
androidx.window.layout.SidecarAdapter$Companion: int getRawSidecarDevicePosture(androidx.window.sidecar.SidecarDeviceState)
androidx.window.core.Version: java.lang.String toString()
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: void start(int,java.util.Collection,int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
androidx.appcompat.app.AppCompatViewInflater: AppCompatViewInflater()
androidx.window.layout.SidecarWindowBackend$Companion: void resetInstance()
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
io.flutter.plugins.webviewflutter.WebChromeClientHostApiImpl$SecureWebChromeClient: WebChromeClientHostApiImpl$SecureWebChromeClient()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$FileChooserMode: io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$FileChooserMode[] values()
androidx.window.core.Version: int getPatch()
androidx.window.embedding.SplitController: void clearRegisteredRules()
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
androidx.window.layout.SidecarWindowBackend: void registerLayoutChangeCallback(android.app.Activity,java.util.concurrent.Executor,androidx.core.util.Consumer)
androidx.core.content.ContextCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.Context,int)
kotlinx.coroutines.NodeList: NodeList()
androidx.window.layout.ActivityCompatHelperApi30: android.graphics.Rect maximumWindowBounds(android.app.Activity)
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.window.layout.WindowInfoTracker$Companion: WindowInfoTracker$Companion()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
kotlinx.coroutines.internal.AtomicOp: AtomicOp()
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: java.lang.CharSequence getDescription()
androidx.window.layout.WindowBackend: void unregisterLayoutChangeCallback(androidx.core.util.Consumer)
androidx.window.layout.WindowInfoTracker$-CC: void reset()
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void close()
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.reflect.Method preHandler()
androidx.window.embedding.EmbeddingAdapter: androidx.window.embedding.SplitInfo translate(androidx.window.extensions.embedding.SplitInfo)
io.flutter.plugins.webviewflutter.WebViewHostApiImpl$WebViewPlatformView: android.webkit.WebChromeClient getWebChromeClient()
androidx.window.layout.SidecarWindowBackend$Companion: SidecarWindowBackend$Companion()
androidx.collection.SimpleArrayMap: SimpleArrayMap()
androidx.appcompat.widget.AppCompatToggleButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.AppCompatSpinner: int getDropDownHorizontalOffset()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getAttributionBehavior()
androidx.window.embedding.EmbeddingAdapter: EmbeddingAdapter()
androidx.window.layout.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.window.layout.WindowMetrics: android.graphics.Rect getBounds()
androidx.core.content.ContextCompat$Api23Impl: int getColor(android.content.Context,int)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.webkit.internal.ApiHelperForM: void postWebMessage(android.webkit.WebView,android.webkit.WebMessage,android.net.Uri)
androidx.window.embedding.SplitRule: SplitRule()
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
androidx.window.layout.FoldingFeature$State: FoldingFeature$State(java.lang.String)
androidx.datastore.preferences.protobuf.ByteString: ByteString()
androidx.window.layout.SidecarWindowBackend$ExtensionListenerImpl: SidecarWindowBackend$ExtensionListenerImpl(androidx.window.layout.SidecarWindowBackend)
androidx.window.layout.DisplayCompatHelperApi28: DisplayCompatHelperApi28()
androidx.window.layout.SidecarWindowBackend: SidecarWindowBackend(androidx.window.layout.ExtensionInterfaceCompat)
androidx.window.layout.SidecarAdapter$Companion: int getSidecarDevicePosture$window_release(androidx.window.sidecar.SidecarDeviceState)
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter: void removeListener(androidx.core.util.Consumer)
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
kotlin.jvm.internal.Lambda: java.lang.String toString()
androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper: ExtensionEmbeddingBackend$SplitListenerWrapper(android.app.Activity,java.util.concurrent.Executor,androidx.core.util.Consumer)
androidx.webkit.internal.ApiHelperForO: android.content.pm.PackageInfo getCurrentWebViewPackage()
androidx.window.layout.HardwareFoldingFeature: androidx.window.layout.FoldingFeature$Orientation getOrientation()
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface: boolean terminate()
io.flutter.view.AccessibilityBridge$CustomAccessibilityAction: AccessibilityBridge$CustomAccessibilityAction()
androidx.window.embedding.SplitRuleParser: androidx.window.embedding.SplitPairFilter parseSplitPairFilter(android.content.Context,android.content.res.XmlResourceParser)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
androidx.webkit.internal.ApiHelperForM: java.lang.CharSequence getDescription(android.webkit.WebResourceError)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
androidx.window.embedding.MatcherUtils: boolean areComponentsMatching$window_release(android.content.ComponentName,android.content.ComponentName)
androidx.window.layout.WindowInfoTracker$Companion: androidx.window.layout.WindowBackend windowBackend$window_release(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
androidx.appcompat.widget.AppCompatSpinner: void setDropDownVerticalOffset(int)
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
androidx.window.embedding.SplitRule$Api30Impl: android.graphics.Rect getBounds(android.view.WindowMetrics)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getWebViewMediaIntegrityApiOverrideRules()
androidx.appcompat.widget.SearchView: void setImeOptions(int)
androidx.window.layout.FoldingFeature: androidx.window.layout.FoldingFeature$State getState()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
androidx.window.layout.DisplayCompatHelperApi28: int safeInsetRight(android.view.DisplayCutout)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.webkit.internal.ApiHelperForM: int getErrorCode(android.webkit.WebResourceError)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
androidx.appcompat.app.AppCompatActivity: AppCompatActivity()
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMaxTextSize()
androidx.window.layout.SidecarAdapter: boolean isEqualSidecarDisplayFeatures(java.util.List,java.util.List)
kotlin.coroutines.AbstractCoroutineContextElement: java.lang.Object fold(java.lang.Object,kotlin.jvm.functions.Function2)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
kotlin.coroutines.AbstractCoroutineContextElement: kotlin.coroutines.CoroutineContext plus(kotlin.coroutines.CoroutineContext)
kotlin.jvm.internal.FunctionReference: kotlin.reflect.KCallable getReflected()
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
androidx.window.layout.ExtensionsWindowLayoutInfoAdapter: androidx.window.layout.FoldingFeature translate$window_release(android.app.Activity,androidx.window.extensions.layout.FoldingFeature)
kotlinx.coroutines.CancelHandler: CancelHandler()
androidx.window.embedding.SplitRule: int hashCode()
androidx.window.layout.FoldingFeature$Orientation: java.lang.String toString()
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
io.flutter.embedding.android.FlutterView: void setVisibility(int)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.window.layout.WindowMetricsCalculatorCompat: android.graphics.Rect computeWindowBoundsIceCreamSandwich$window_release(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
androidx.window.layout.SidecarCompat$Companion: SidecarCompat$Companion()
kotlinx.coroutines.MainCoroutineDispatcher: MainCoroutineDispatcher()
androidx.window.layout.SidecarCompat$registerConfigurationChangeListener$configChangeObserver$1: void onLowMemory()
androidx.window.embedding.EmbeddingAdapter: java.util.function.Predicate translateIntentPredicates(java.util.Set)
androidx.window.embedding.SplitRule: SplitRule(int,int,float,int)
androidx.window.layout.SidecarAdapter: SidecarAdapter()
androidx.window.layout.SidecarWindowBackend$Companion: SidecarWindowBackend$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
androidx.window.layout.SidecarAdapter: boolean isEqualSidecarDeviceState(androidx.window.sidecar.SidecarDeviceState,androidx.window.sidecar.SidecarDeviceState)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
androidx.lifecycle.ViewModel: ViewModel()
androidx.datastore.preferences.PreferencesProto$PreferenceMap: PreferencesProto$PreferenceMap()
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
androidx.window.layout.HardwareFoldingFeature: android.graphics.Rect getBounds()
androidx.window.layout.ExtensionsWindowLayoutInfoAdapter: androidx.window.layout.WindowLayoutInfo translate$window_release(android.app.Activity,androidx.window.extensions.layout.WindowLayoutInfo)
kotlin.jvm.internal.FunctionReference: kotlin.reflect.KCallable computeReflected()
androidx.appcompat.widget.AppCompatSpinner: void setDropDownHorizontalOffset(int)
androidx.window.layout.WindowMetricsCalculatorCompat: android.graphics.Point getRealSizeForDisplay$window_release(android.view.Display)
kotlin.coroutines.AbstractCoroutineContextElement: kotlin.coroutines.CoroutineContext$Key getKey()
androidx.appcompat.widget.LinearLayoutCompat: LinearLayoutCompat(android.content.Context,android.util.AttributeSet)
androidx.window.layout.SidecarCompat$DistinctElementCallback: SidecarCompat$DistinctElementCallback(androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.appcompat.widget.AppCompatEditText: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
androidx.window.embedding.SplitPlaceholderRule: android.content.Intent getPlaceholderIntent()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
androidx.window.embedding.SplitPlaceholderRule: SplitPlaceholderRule(java.util.Set,android.content.Intent,int,int,float,int,int,kotlin.jvm.internal.DefaultConstructorMarker)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.window.layout.WindowMetricsCalculatorDecorator: androidx.window.layout.WindowMetricsCalculator decorate(androidx.window.layout.WindowMetricsCalculator)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.window.layout.HardwareFoldingFeature$Type$Companion: HardwareFoldingFeature$Type$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.window.embedding.ExtensionEmbeddingBackend: java.util.concurrent.CopyOnWriteArrayList getSplitChangeCallbacks()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
kotlin.coroutines.jvm.internal.SuspendLambda: java.lang.String toString()
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.webkit.internal.ApiHelperForP: void start(android.webkit.TracingController,androidx.webkit.TracingConfig)
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
androidx.window.embedding.EmbeddingCompat$Companion: androidx.window.extensions.embedding.ActivityEmbeddingComponent embeddingComponent()
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface: java.lang.Object getOrCreatePeer(java.util.concurrent.Callable)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: java.lang.String getName()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setDropDownBackgroundResource(int)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
androidx.window.embedding.ActivityStack: java.lang.String toString()
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.lifecycle.ViewModelProvider$OnRequeryFactory: ViewModelProvider$OnRequeryFactory()
androidx.window.layout.ExtensionWindowLayoutInfoBackend$MulticastConsumer: void addListener(androidx.core.util.Consumer)
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.window.embedding.SplitInfo: androidx.window.embedding.ActivityStack getPrimaryActivityStack()
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
androidx.window.layout.WindowMetricsCalculator$Companion$decorator$1: WindowMetricsCalculator$Companion$decorator$1()
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.window.core.Version: androidx.window.core.Version access$getVERSION_1_0$cp()
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.window.layout.SidecarCompat$DistinctElementCallback: void onWindowLayoutChanged(android.app.Activity,androidx.window.layout.WindowLayoutInfo)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
androidx.window.core.Version: androidx.window.core.Version parse(java.lang.String)
androidx.window.core.Bounds: boolean isZero()
androidx.appcompat.widget.DropDownListView: void setSelector(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportButtonTintList()
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
kotlin.jvm.internal.FunctionReference: boolean isSuspend()
kotlin.coroutines.jvm.internal.ContinuationImpl: void releaseIntercepted()
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
androidx.window.layout.HardwareFoldingFeature$Companion: HardwareFoldingFeature$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$ConsoleMessageLevel: io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$ConsoleMessageLevel valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void removeWebMessageListener(java.lang.String)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$ExtendableMessage: GeneratedMessageLite$ExtendableMessage()
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatEditText: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$WebResourceErrorData: GeneratedAndroidWebView$WebResourceErrorData()
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
androidx.window.embedding.EmbeddingAdapter: java.lang.Object component1(android.util.Pair)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
androidx.window.core.Version$Companion: androidx.window.core.Version parse(java.lang.String)
androidx.window.core.Bounds: int getLeft()
androidx.window.layout.SidecarAdapter$Companion: void setSidecarDisplayFeatures(androidx.window.sidecar.SidecarWindowLayoutInfo,java.util.List)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.window.layout.HardwareFoldingFeature: java.lang.String toString()
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: androidx.window.layout.WindowLayoutInfo getLastInfo()
androidx.appcompat.widget.AppCompatSpinner: void setPrompt(java.lang.CharSequence)
androidx.window.embedding.ExtensionEmbeddingBackend$EmbeddingCallbackImpl: void onSplitInfoChanged(java.util.List)
androidx.datastore.preferences.protobuf.CodedOutputStream: CodedOutputStream()
androidx.window.java.R: R()
androidx.window.embedding.EmbeddingCompat$Companion: java.lang.Integer getExtensionApiLevel()
androidx.window.embedding.SplitPairFilter: boolean matchesActivityIntentPair(android.app.Activity,android.content.Intent)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedHttpError(android.webkit.WebView,android.webkit.WebResourceRequest,android.webkit.WebResourceResponse)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
androidx.window.layout.DisplayCompatHelperApi17: DisplayCompatHelperApi17()
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
androidx.window.embedding.SplitController: void access$setStaticSplitRules(androidx.window.embedding.SplitController,java.util.Set)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getUserAgentMetadataMap()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
androidx.webkit.internal.ApiHelperForP: java.lang.ClassLoader getWebViewClassLoader()
kotlin.jvm.internal.CallableReference: kotlin.reflect.KDeclarationContainer getOwner()
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.window.embedding.EmptyEmbeddingComponent: EmptyEmbeddingComponent()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.window.embedding.ActivityStack: ActivityStack(java.util.List,boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
androidx.window.core.Version: java.lang.String getDescription()
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.window.layout.SidecarCompat$Companion: android.os.IBinder getActivityWindowToken$window_release(android.app.Activity)
kotlinx.coroutines.JobCancellingNode: JobCancellingNode()
androidx.window.embedding.ExtensionEmbeddingBackend: java.util.Set getSplitRules()
androidx.window.layout.ExtensionsWindowLayoutInfoAdapter: boolean validBounds(android.app.Activity,androidx.window.core.Bounds)
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessageWithPayload(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
androidx.window.embedding.EmbeddingAdapter: java.util.List translate(java.util.List)
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.window.embedding.ExtensionEmbeddingBackend: ExtensionEmbeddingBackend(androidx.window.embedding.EmbeddingInterfaceCompat)
org.chromium.support_lib_boundary.WebAuthnCallbackBoundaryInterface: void onResult(int,android.content.Intent)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
androidx.window.layout.SidecarCompat: void onWindowLayoutChangeListenerRemoved(android.app.Activity)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter: void addListener(java.util.concurrent.Executor,androidx.core.util.Consumer,kotlinx.coroutines.flow.Flow)
androidx.window.layout.SidecarCompat: void registerConfigurationChangeListener(android.app.Activity)
kotlin.reflect.KVisibility: kotlin.reflect.KVisibility[] values()
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.datastore.preferences.protobuf.UnsafeUtil: UnsafeUtil()
androidx.window.core.Version: int compareTo(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: int getType()
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1: java.lang.Object invokeSuspend(java.lang.Object)
androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback: SidecarCompat$DistinctSidecarElementCallback(androidx.window.layout.SidecarAdapter,androidx.window.sidecar.SidecarInterface$SidecarCallback)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebauthnSupport(int)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.String[] getSupportedFeatures()
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
androidx.appcompat.widget.AbsActionBarView: void setVisibility(int)
androidx.window.embedding.EmbeddingCompat: EmbeddingCompat()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDarkBehavior()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.window.embedding.SplitRuleParser: androidx.window.embedding.ActivityFilter parseActivityFilter(android.content.Context,android.content.res.XmlResourceParser)
androidx.appcompat.widget.AppCompatButton: void setAutoSizeTextTypeWithDefaults(int)
kotlin.jvm.internal.FunctionReference: FunctionReference(int)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatToggleButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.window.layout.WindowMetrics: boolean equals(java.lang.Object)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebViewClient getWebViewClient()
androidx.window.embedding.SplitPairRule: SplitPairRule(java.util.Set,boolean,boolean,boolean,int,int,float,int)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.window.layout.WindowMetricsCalculator$Companion$reset$1: WindowMetricsCalculator$Companion$reset$1()
androidx.fragment.app.FragmentContainerView: void setLayoutTransition(android.animation.LayoutTransition)
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
androidx.window.layout.HardwareFoldingFeature$Type: androidx.window.layout.HardwareFoldingFeature$Type access$getHINGE$cp()
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundResource(int)
androidx.window.layout.SidecarCompat$TranslatingCallback: SidecarCompat$TranslatingCallback(androidx.window.layout.SidecarCompat)
kotlin.coroutines.jvm.internal.BaseContinuationImpl: void releaseIntercepted()
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.window.embedding.EmbeddingInterfaceCompat$EmbeddingCallbackInterface: void onSplitInfoChanged(java.util.List)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundResource(int)
androidx.window.embedding.SplitPairRule: java.util.Set getFilters()
androidx.window.layout.HardwareFoldingFeature$Type$Companion: androidx.window.layout.HardwareFoldingFeature$Type getFOLD()
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler addDocumentStartJavaScript(java.lang.String,java.lang.String[])
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.window.core.Version$Companion: androidx.window.core.Version getCURRENT()
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.window.embedding.SplitPlaceholderRule: SplitPlaceholderRule(java.util.Set,android.content.Intent,int,int,float,int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.String getData()
androidx.window.embedding.ActivityFilter: boolean matchesActivity(android.app.Activity)
androidx.appcompat.widget.AppCompatButton: void setSupportAllCaps(boolean)
androidx.window.layout.HardwareFoldingFeature$Type: java.lang.String toString()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
androidx.webkit.internal.ApiHelperForO: android.webkit.WebViewClient getWebViewClient(android.webkit.WebView)
androidx.webkit.internal.ApiHelperForO: void setSafeBrowsingEnabled(android.webkit.WebSettings,boolean)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getSafeBrowsingEnabled()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
androidx.window.core.Version: Version(int,int,int,java.lang.String,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProfileStore()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getWebkitToCompatConverter()
androidx.webkit.internal.ApiHelperForM: void postMessage(android.webkit.WebMessagePort,android.webkit.WebMessage)
androidx.window.embedding.SplitController: void registerRule(androidx.window.embedding.EmbeddingRule)
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportButtonTintList()
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: SidecarWindowBackend$WindowLayoutChangeCallbackWrapper(android.app.Activity,java.util.concurrent.Executor,androidx.core.util.Consumer)
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setWebViewRendererClient(java.lang.reflect.InvocationHandler)
androidx.window.embedding.EmbeddingAdapter: java.util.function.Predicate translateParentMetricsPredicate(androidx.window.embedding.SplitRule)
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
androidx.window.layout.HardwareFoldingFeature: int hashCode()
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact valueOf(java.lang.String)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingAllowlist(java.util.Set,android.webkit.ValueCallback)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VClipPath: VectorDrawableCompat$VClipPath()
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface: void onPostMessage(android.webkit.WebView,java.lang.reflect.InvocationHandler,android.net.Uri,boolean,java.lang.reflect.InvocationHandler)
androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter: WindowInfoTrackerCallbackAdapter(androidx.window.layout.WindowInfoTracker)
androidx.core.view.accessibility.AccessibilityViewCommand$SetTextArguments: AccessibilityViewCommand$SetTextArguments()
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
androidx.window.layout.DisplayFeature: android.graphics.Rect getBounds()
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.fragment.app.FragmentContainerView: void setOnApplyWindowInsetsListener(android.view.View$OnApplyWindowInsetsListener)
androidx.window.core.Version: int getMajor()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
kotlinx.coroutines.CompletionHandlerBase: CompletionHandlerBase()
io.flutter.embedding.engine.loader.FlutterLoader: FlutterLoader()
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onSafeBrowsingHit(android.webkit.WebView,android.webkit.WebResourceRequest,int,java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundResource(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
androidx.fragment.app.Fragment$OnPreAttachedListener: Fragment$OnPreAttachedListener()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: java.lang.String getVariationsHeader()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
androidx.window.layout.SidecarCompat: void register(android.os.IBinder,android.app.Activity)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerController()
androidx.window.layout.WindowMetricsCalculatorCompat: androidx.window.layout.WindowMetrics computeCurrentWindowMetrics(android.app.Activity)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean isAlgorithmicDarkeningAllowed()
androidx.window.embedding.SplitController$Companion: SplitController$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.window.layout.WindowMetricsCalculatorCompat: void getRectSizeFromDisplay(android.app.Activity,android.graphics.Rect)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setEnterpriseAuthenticationAppLinkPolicyEnabled(boolean)
androidx.window.layout.WindowMetricsCalculatorCompat: android.view.DisplayCutout getCutoutForDisplay(android.view.Display)
androidx.window.layout.SidecarCompat$Companion: androidx.window.core.Version getSidecarVersion()
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.window.embedding.MatcherUtils: MatcherUtils()
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
androidx.window.embedding.SplitController: androidx.window.embedding.SplitController access$getGlobalInstance$cp()
androidx.window.layout.WindowMetricsCalculator$Companion: WindowMetricsCalculator$Companion()
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeStepGranularity()
androidx.core.graphics.drawable.IconCompat: IconCompat()
org.chromium.support_lib_boundary.util.BoundaryInterfaceReflectionUtil: BoundaryInterfaceReflectionUtil()
androidx.fragment.app.FragmentManagerState: FragmentManagerState()
kotlin.jvm.internal.CallableReference: java.util.List getParameters()
androidx.window.embedding.SplitPairFilter: java.lang.String getSecondaryActivityIntentAction()
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
androidx.window.embedding.ExtensionEmbeddingBackend: void access$setGlobalInstance$cp(androidx.window.embedding.ExtensionEmbeddingBackend)
kotlinx.coroutines.flow.StateFlowSlot: StateFlowSlot()
androidx.window.embedding.SplitRule: boolean equals(java.lang.Object)
androidx.window.embedding.SplitController: void access$setGlobalInstance$cp(androidx.window.embedding.SplitController)
androidx.window.embedding.EmbeddingAdapter: boolean translateActivityPairPredicates$lambda-1(androidx.window.embedding.EmbeddingAdapter,java.util.Set,android.util.Pair)
kotlin.coroutines.jvm.internal.BaseContinuationImpl: kotlin.coroutines.Continuation getCompletion()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setDisabledActionModeMenuItems(int)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.window.embedding.ExtensionEmbeddingBackend$Companion: androidx.window.embedding.ExtensionEmbeddingBackend getInstance()
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
androidx.window.layout.HardwareFoldingFeature: boolean isSeparating()
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler[] getPorts()
androidx.window.embedding.EmbeddingCompat$Companion: boolean isEmbeddingAvailable()
androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper: void accept(java.util.List)
androidx.window.layout.SidecarWindowBackend$WindowLayoutChangeCallbackWrapper: android.app.Activity getActivity()
androidx.window.layout.ExtensionInterfaceCompat: void setExtensionCallback(androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: VectorDrawableCompat$VGroup()
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.loader.app.LoaderManagerImpl$LoaderViewModel: LoaderManagerImpl$LoaderViewModel()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
androidx.window.layout.FoldingFeature: androidx.window.layout.FoldingFeature$Orientation getOrientation()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.datastore.core.SingleProcessDataStore$Message: SingleProcessDataStore$Message()
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
androidx.window.layout.WindowMetricsCalculator$Companion: void reset()
kotlin.jvm.internal.Ref$BooleanRef: Ref$BooleanRef()
androidx.window.layout.DisplayCompatHelperApi28: int safeInsetBottom(android.view.DisplayCutout)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
androidx.window.layout.WindowInfoTracker$-CC: void overrideDecorator(androidx.window.layout.WindowInfoTrackerDecorator)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
androidx.window.layout.HardwareFoldingFeature: androidx.window.layout.HardwareFoldingFeature$Type getType$window_release()
kotlin.jvm.internal.FunctionReferenceImpl: FunctionReferenceImpl(int,java.lang.Class,java.lang.String,java.lang.String,int)
androidx.window.core.Version$Companion: Version$Companion()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
androidx.collection.ArraySet: ArraySet()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$600(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.window.layout.SidecarWindowBackend: androidx.window.layout.SidecarWindowBackend access$getGlobalInstance$cp()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onPageCommitVisible(android.webkit.WebView,java.lang.String)
kotlin.jvm.internal.CallableReference: kotlin.reflect.KVisibility getVisibility()
kotlin.coroutines.jvm.internal.SuspendLambda: int getArity()
androidx.window.embedding.ActivityFilter: android.content.ComponentName getComponentName()
androidx.window.embedding.ExtensionEmbeddingBackend: androidx.window.embedding.ExtensionEmbeddingBackend access$getGlobalInstance$cp()
kotlin.jvm.internal.FunctionReferenceImpl: FunctionReferenceImpl(int,kotlin.reflect.KDeclarationContainer,java.lang.String,java.lang.String)
androidx.window.embedding.SplitPlaceholderRule: int hashCode()
kotlin.jvm.internal.FunctionReference: boolean isOperator()
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
kotlin.coroutines.jvm.internal.BaseContinuationImpl: void resumeWith(java.lang.Object)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
androidx.webkit.internal.ApiHelperForM: android.webkit.WebMessagePort[] createWebMessageChannel(android.webkit.WebView)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getStatics()
androidx.window.embedding.EmbeddingCompat$Companion: EmbeddingCompat$Companion()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List)
androidx.window.layout.SidecarAdapter$Companion: java.util.List getSidecarDisplayFeatures(androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.window.embedding.ExtensionEmbeddingBackend$Companion: androidx.window.embedding.EmbeddingInterfaceCompat initAndVerifyEmbeddingExtension()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.window.layout.HardwareFoldingFeature$Type$Companion: HardwareFoldingFeature$Type$Companion()
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter: void addWindowLayoutInfoListener(android.app.Activity,java.util.concurrent.Executor,androidx.core.util.Consumer)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getEnterpriseAuthenticationAppLinkPolicyEnabled()
androidx.appcompat.graphics.drawable.AnimatedStateListDrawableCompat: AnimatedStateListDrawableCompat()
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
androidx.window.layout.FoldingFeature$Orientation$Companion: FoldingFeature$Orientation$Companion()
androidx.window.layout.ExtensionInterfaceCompat: boolean validateExtensionInterface()
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void showInterstitial(boolean)
androidx.window.embedding.EmbeddingBackend: boolean isSplitSupported()
androidx.window.layout.WindowMetricsCalculator$Companion$decorator$1: java.lang.Object invoke(java.lang.Object)
androidx.appcompat.widget.ResourceManagerInternal$DrawableDelegate: ResourceManagerInternal$DrawableDelegate()
androidx.window.layout.WindowMetricsCalculatorCompat: WindowMetricsCalculatorCompat()
androidx.window.layout.WindowMetricsCalculatorCompat: android.graphics.Rect computeWindowBoundsP$window_release(android.app.Activity)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
io.flutter.plugins.webviewflutter.WebViewHostApiImpl$WebViewPlatformView: void setWebChromeClient(android.webkit.WebChromeClient)
androidx.appcompat.widget.AppCompatSpinner: android.content.res.ColorStateList getSupportBackgroundTintList()
kotlin.jvm.internal.PropertyReference: PropertyReference()
androidx.window.layout.WindowInfoTrackerImpl$windowLayoutInfo$1: void invokeSuspend$lambda-0(kotlinx.coroutines.channels.Channel,androidx.window.layout.WindowLayoutInfo)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
androidx.window.layout.WindowInfoTrackerImpl$Companion: WindowInfoTrackerImpl$Companion()
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
androidx.window.embedding.ActivityStack: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
androidx.window.embedding.EmbeddingAdapter: boolean translateActivityPredicates$lambda-6(java.util.Set,android.app.Activity)
androidx.window.core.Version$bigInteger$2: Version$bigInteger$2(androidx.window.core.Version)
androidx.webkit.internal.ApiHelperForOMR1: void backToSafety(android.webkit.SafeBrowsingResponse,boolean)
androidx.window.layout.SidecarWindowBackend$Companion: boolean isSidecarVersionSupported(androidx.window.core.Version)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getBlockNetworkLoads()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setOffscreenPreRaster(boolean)
androidx.window.embedding.SplitPairRule: boolean getFinishSecondaryWithPrimary()
androidx.window.java.layout.WindowInfoTrackerCallbackAdapter$addListener$1$1: java.lang.Object invoke(java.lang.Object,java.lang.Object)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.window.layout.WindowLayoutInfo: java.util.List getDisplayFeatures()
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.window.embedding.ActivityStack: ActivityStack(java.util.List,boolean,int,kotlin.jvm.internal.DefaultConstructorMarker)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getTracingController()
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.datastore.preferences.protobuf.UnknownFieldSetLite: UnknownFieldSetLite()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: AppCompatTextViewAutoSizeHelper$Impl23()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
androidx.window.layout.WindowInfoTracker$Companion: void overrideDecorator(androidx.window.layout.WindowInfoTrackerDecorator)
androidx.window.embedding.ActivityRule: int hashCode()
kotlin.internal.jdk7.JDK7PlatformImplementations: JDK7PlatformImplementations()
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.window.core.Bounds: int getRight()
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
androidx.arch.core.executor.ArchTaskExecutor: ArchTaskExecutor()
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.window.embedding.EmbeddingBackend: void registerRule(androidx.window.embedding.EmbeddingRule)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebMessagePort(java.lang.reflect.InvocationHandler)
androidx.window.embedding.SplitPlaceholderRule: java.util.Set getFilters()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: AppCompatTextViewAutoSizeHelper$Impl29()
kotlinx.coroutines.scheduling.WorkQueue: WorkQueue()
androidx.window.embedding.SplitPairRule: boolean getClearTop()
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.AppCompatEditText: android.text.Editable getText()
androidx.window.layout.FoldingFeature: androidx.window.layout.FoldingFeature$OcclusionType getOcclusionType()
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.vectordrawable.graphics.drawable.AnimatedVectorDrawableCompat: AnimatedVectorDrawableCompat()
androidx.core.view.accessibility.AccessibilityViewCommand$ScrollToPositionArguments: AccessibilityViewCommand$ScrollToPositionArguments()
androidx.window.embedding.MatcherUtils: boolean areActivityOrIntentComponentsMatching$window_release(android.app.Activity,android.content.ComponentName)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
androidx.window.embedding.EmbeddingCompat: void setSplitRules(java.util.Set)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.window.layout.WindowInfoTrackerImpl: WindowInfoTrackerImpl(androidx.window.layout.WindowMetricsCalculator,androidx.window.layout.WindowBackend)
kotlin.jvm.internal.CallableReference: kotlin.reflect.KCallable getReflected()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface: void onComplete(long)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
androidx.appcompat.widget.AppCompatButton: int[] getAutoSizeTextAvailableSizes()
kotlinx.coroutines.channels.Send: Send()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
androidx.window.embedding.SplitPairRule: boolean equals(java.lang.Object)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.appcompat.widget.AppCompatCheckedTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.plugins.webviewflutter.GeneratedAndroidWebView$WebResourceResponseData: GeneratedAndroidWebView$WebResourceResponseData()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.github.edufolly.flutterbluetoothserial.FlutterBluetoothSerialPlugin: FlutterBluetoothSerialPlugin()
androidx.window.core.Bounds: android.graphics.Rect toRect()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
kotlin.jvm.internal.CallableReference: boolean isAbstract()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.core.content.ContextCompat$Api16Impl: void startActivity(android.content.Context,android.content.Intent,android.os.Bundle)
androidx.core.content.ContextCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.window.embedding.SplitController$Companion: SplitController$Companion()
androidx.window.layout.WindowLayoutInfo: java.lang.String toString()
kotlinx.coroutines.internal.LockFreeLinkedListNode: LockFreeLinkedListNode()
androidx.appcompat.widget.AppCompatButton: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
